{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\QuizInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Tb<PERSON>lock, TbChevronLeft, TbChevronRight, TbCheck, TbX, TbFlag, TbAlertCircle } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizInterface = ({\n  quiz,\n  questions = [],\n  onSubmit,\n  onExit,\n  className = ''\n}) => {\n  _s();\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeRemaining, setTimeRemaining] = useState(((quiz === null || quiz === void 0 ? void 0 : quiz.duration) || 30) * 60); // Convert to seconds\n  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);\n  const currentQuestion = questions[currentQuestionIndex];\n  const totalQuestions = questions.length;\n  const progress = (currentQuestionIndex + 1) / totalQuestions * 100;\n\n  // Timer effect\n  useEffect(() => {\n    if (timeRemaining <= 0) {\n      handleSubmit();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeRemaining(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeRemaining]);\n\n  // Format time as MM:SS\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerChange = (questionId, answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < totalQuestions - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n  const goToQuestion = index => {\n    setCurrentQuestionIndex(index);\n  };\n\n  // Submit quiz\n  const handleSubmit = () => {\n    onSubmit && onSubmit(answers);\n  };\n\n  // Render question based on type\n  const renderQuestion = () => {\n    if (!currentQuestion) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n          className: \"w-12 h-12 mx-auto mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Question not available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Debug log to see the question structure\n    console.log('Current question:', currentQuestion);\n    console.log('Question options:', currentQuestion.options);\n    console.log('Question type:', currentQuestion.type || currentQuestion.answerType);\n    switch (currentQuestion.type || currentQuestion.answerType) {\n      case 'multiple-choice':\n      case 'mcq':\n        // Ensure options is an array\n        const options = Array.isArray(currentQuestion.options) ? currentQuestion.options : [];\n        if (options.length === 0) {\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n              className: \"w-12 h-12 mx-auto mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No options available for this question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this);\n        }\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: options.map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n            const isSelected = answers[currentQuestion._id] === option;\n            return /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              onClick: () => handleAnswerChange(currentQuestion._id, option),\n              className: `\n                    w-full p-4 rounded-lg border-2 text-left transition-all duration-200\n                    ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'}\n                  `,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `\n                      w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                      ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-500'}\n                    `,\n                  children: optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-1\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this);\n      case 'fill-in-the-blank':\n      case 'text':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: answers[currentQuestion._id] || '',\n            onChange: e => handleAnswerChange(currentQuestion._id, e.target.value),\n            placeholder: \"Type your answer here...\",\n            className: \"w-full p-4 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none\",\n            rows: 4\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this);\n      case 'image':\n        // Ensure options is an array for image questions too\n        const imageOptions = Array.isArray(currentQuestion.options) ? currentQuestion.options : [];\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [currentQuestion.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentQuestion.imageUrl,\n              alt: \"Question\",\n              className: \"max-w-full h-auto rounded-lg mx-auto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), imageOptions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n              className: \"w-12 h-12 mx-auto mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No options available for this image question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: imageOptions.map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index);\n              const isSelected = answers[currentQuestion._id] === option;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleAnswerChange(currentQuestion._id, option),\n                className: `\n                        w-full p-4 rounded-lg border-2 text-left transition-all duration-200\n                        ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 bg-white hover:border-gray-300'}\n                      `,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `\n                          w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                          ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-500'}\n                        `,\n                    children: optionLetter\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: option\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8 text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n            className: \"w-12 h-12 mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Unsupported question type: \", currentQuestion.type || currentQuestion.answerType]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen bg-gray-50 flex flex-col ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b sticky top-0 z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: (quiz === null || quiz === void 0 ? void 0 : quiz.name) || 'Quiz'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: (quiz === null || quiz === void 0 ? void 0 : quiz.subject) || 'Subject'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `\n              flex items-center gap-2 px-4 py-2 rounded-lg font-mono text-lg font-bold\n              ${timeRemaining <= 300 ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'}\n            `,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), formatTime(timeRemaining)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between text-sm text-gray-600 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Question \", currentQuestionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round(progress), \"% Complete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-blue-500 h-2 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progress}%`\n              },\n              transition: {\n                duration: 0.3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 max-w-4xl mx-auto w-full px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-xl shadow-sm border p-6 lg:p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n              children: (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.name) || (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.question) || 'Question not available'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.description) && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4\",\n              children: currentQuestion.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), renderQuestion()]\n        }, currentQuestionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t sticky bottom-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestionIndex === 0,\n            className: `\n                flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors\n                ${currentQuestionIndex === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(TbChevronLeft, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 overflow-x-auto max-w-md\",\n            children: questions && Array.isArray(questions) ? questions.map((question, index) => {\n              // Safety check for question object\n              if (!question || typeof question !== 'object') {\n                console.warn('Invalid question at index:', index, question);\n                return null;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => goToQuestion(index),\n                className: `\n                      w-8 h-8 rounded-full text-sm font-medium transition-colors flex-shrink-0\n                      ${index === currentQuestionIndex ? 'bg-blue-500 text-white' : answers[question._id] ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}\n                    `,\n                children: index + 1\n              }, question._id || index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this);\n            }) : null\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), currentQuestionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSubmitConfirm(true),\n            className: \"flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), \"Submit Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToNext,\n            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors\",\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(TbChevronRight, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: showSubmitConfirm && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0.9,\n            opacity: 0\n          },\n          animate: {\n            scale: 1,\n            opacity: 1\n          },\n          exit: {\n            scale: 0.9,\n            opacity: 0\n          },\n          className: \"bg-white rounded-xl p-6 max-w-md w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold text-gray-900 mb-4\",\n            children: \"Submit Quiz?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Are you sure you want to submit your quiz? You won't be able to change your answers after submission.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSubmitConfirm(false),\n              className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmit,\n              className: \"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n              children: \"Submit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizInterface, \"xoS+oLqbqtVr+EDICtQ8Jyf/8MM=\");\n_c = QuizInterface;\nexport default QuizInterface;\nvar _c;\n$RefreshReg$(_c, \"QuizInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "AnimatePresence", "TbClock", "TbChevronLeft", "TbChevronRight", "TbCheck", "TbX", "TbFlag", "TbAlertCircle", "jsxDEV", "_jsxDEV", "QuizInterface", "quiz", "questions", "onSubmit", "onExit", "className", "_s", "currentQuestionIndex", "setCurrentQuestionIndex", "answers", "setAnswers", "timeRemaining", "setTimeRemaining", "duration", "showSubmitConfirm", "setShowSubmitConfirm", "currentQuestion", "totalQuestions", "length", "progress", "handleSubmit", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "handleAnswerChange", "questionId", "answer", "goToNext", "goToPrevious", "goToQuestion", "index", "renderQuestion", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "console", "log", "options", "type", "answerType", "Array", "isArray", "map", "option", "optionLetter", "String", "fromCharCode", "isSelected", "_id", "button", "whileHover", "scale", "whileTap", "onClick", "value", "onChange", "e", "target", "placeholder", "rows", "imageOptions", "imageUrl", "src", "alt", "name", "subject", "round", "div", "initial", "width", "animate", "transition", "mode", "opacity", "x", "exit", "question", "description", "disabled", "warn", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/QuizInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Tb<PERSON>lock,\n  TbChevronLeft,\n  TbChevronRight,\n  TbCheck,\n  TbX,\n  TbFlag,\n  TbAlertCircle,\n} from 'react-icons/tb';\n\nconst QuizInterface = ({\n  quiz,\n  questions = [],\n  onSubmit,\n  onExit,\n  className = ''\n}) => {\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeRemaining, setTimeRemaining] = useState((quiz?.duration || 30) * 60); // Convert to seconds\n  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const totalQuestions = questions.length;\n  const progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;\n\n  // Timer effect\n  useEffect(() => {\n    if (timeRemaining <= 0) {\n      handleSubmit();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeRemaining(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeRemaining]);\n\n  // Format time as MM:SS\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerChange = (questionId, answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < totalQuestions - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  const goToQuestion = (index) => {\n    setCurrentQuestionIndex(index);\n  };\n\n  // Submit quiz\n  const handleSubmit = () => {\n    onSubmit && onSubmit(answers);\n  };\n\n  // Render question based on type\n  const renderQuestion = () => {\n    if (!currentQuestion) {\n      return (\n        <div className=\"text-center py-8 text-gray-500\">\n          <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n          <p>Question not available</p>\n        </div>\n      );\n    }\n\n    // Debug log to see the question structure\n    console.log('Current question:', currentQuestion);\n    console.log('Question options:', currentQuestion.options);\n    console.log('Question type:', currentQuestion.type || currentQuestion.answerType);\n\n    switch (currentQuestion.type || currentQuestion.answerType) {\n      case 'multiple-choice':\n      case 'mcq':\n        // Ensure options is an array\n        const options = Array.isArray(currentQuestion.options) ? currentQuestion.options : [];\n\n        if (options.length === 0) {\n          return (\n            <div className=\"text-center py-8 text-gray-500\">\n              <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n              <p>No options available for this question</p>\n            </div>\n          );\n        }\n\n        return (\n          <div className=\"space-y-3\">\n            {options.map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n              const isSelected = answers[currentQuestion._id] === option;\n\n              return (\n                <motion.button\n                  key={index}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  onClick={() => handleAnswerChange(currentQuestion._id, option)}\n                  className={`\n                    w-full p-4 rounded-lg border-2 text-left transition-all duration-200\n                    ${isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900'\n                      : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'\n                    }\n                  `}\n                >\n                  <div className=\"flex items-center gap-3\">\n                    <div className={`\n                      w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                      ${isSelected\n                        ? 'border-blue-500 bg-blue-500 text-white'\n                        : 'border-gray-300 text-gray-500'\n                      }\n                    `}>\n                      {optionLetter}\n                    </div>\n                    <span className=\"flex-1\">{option}</span>\n                  </div>\n                </motion.button>\n              );\n            })}\n          </div>\n        );\n\n      case 'fill-in-the-blank':\n      case 'text':\n        return (\n          <div>\n            <textarea\n              value={answers[currentQuestion._id] || ''}\n              onChange={(e) => handleAnswerChange(currentQuestion._id, e.target.value)}\n              placeholder=\"Type your answer here...\"\n              className=\"w-full p-4 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none\"\n              rows={4}\n            />\n          </div>\n        );\n\n      case 'image':\n        // Ensure options is an array for image questions too\n        const imageOptions = Array.isArray(currentQuestion.options) ? currentQuestion.options : [];\n\n        return (\n          <div className=\"space-y-4\">\n            {currentQuestion.imageUrl && (\n              <div className=\"bg-gray-100 rounded-lg p-4\">\n                <img\n                  src={currentQuestion.imageUrl}\n                  alt=\"Question\"\n                  className=\"max-w-full h-auto rounded-lg mx-auto\"\n                />\n              </div>\n            )}\n\n            {imageOptions.length === 0 ? (\n              <div className=\"text-center py-8 text-gray-500\">\n                <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n                <p>No options available for this image question</p>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {imageOptions.map((option, index) => {\n                  const optionLetter = String.fromCharCode(65 + index);\n                  const isSelected = answers[currentQuestion._id] === option;\n\n                  return (\n                    <button\n                      key={index}\n                      onClick={() => handleAnswerChange(currentQuestion._id, option)}\n                      className={`\n                        w-full p-4 rounded-lg border-2 text-left transition-all duration-200\n                        ${isSelected\n                          ? 'border-blue-500 bg-blue-50 text-blue-900'\n                          : 'border-gray-200 bg-white hover:border-gray-300'\n                        }\n                      `}\n                    >\n                      <div className=\"flex items-center gap-3\">\n                        <div className={`\n                          w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                          ${isSelected\n                            ? 'border-blue-500 bg-blue-500 text-white'\n                            : 'border-gray-300 text-gray-500'\n                          }\n                        `}>\n                          {optionLetter}\n                        </div>\n                        <span>{option}</span>\n                      </div>\n                    </button>\n                  );\n                })}\n              </div>\n            )}\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"text-center py-8 text-gray-500\">\n            <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n            <p>Unsupported question type: {currentQuestion.type || currentQuestion.answerType}</p>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 flex flex-col ${className}`}>\n      {/* Top Bar */}\n      <div className=\"bg-white shadow-sm border-b sticky top-0 z-10\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Title */}\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900\">{quiz?.name || 'Quiz'}</h1>\n              <p className=\"text-sm text-gray-600\">{quiz?.subject || 'Subject'}</p>\n            </div>\n\n            {/* Timer */}\n            <div className={`\n              flex items-center gap-2 px-4 py-2 rounded-lg font-mono text-lg font-bold\n              ${timeRemaining <= 300 ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'}\n            `}>\n              <TbClock className=\"w-5 h-5\" />\n              {formatTime(timeRemaining)}\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4\">\n            <div className=\"flex items-center justify-between text-sm text-gray-600 mb-2\">\n              <span>Question {currentQuestionIndex + 1} of {totalQuestions}</span>\n              <span>{Math.round(progress)}% Complete</span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-blue-500 h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progress}%` }}\n                transition={{ duration: 0.3 }}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Question Content */}\n      <div className=\"flex-1 max-w-4xl mx-auto w-full px-4 py-8\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentQuestionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-xl shadow-sm border p-6 lg:p-8\"\n          >\n            {/* Question */}\n            <div className=\"mb-8\">\n              <h2 className=\"text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                {currentQuestion?.name || currentQuestion?.question || 'Question not available'}\n              </h2>\n              \n              {currentQuestion?.description && (\n                <p className=\"text-gray-600 mb-4\">{currentQuestion.description}</p>\n              )}\n            </div>\n\n            {/* Answer Options */}\n            {renderQuestion()}\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Bottom Navigation */}\n      <div className=\"bg-white border-t sticky bottom-0\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Previous Button */}\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestionIndex === 0}\n              className={`\n                flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors\n                ${currentQuestionIndex === 0\n                  ? 'text-gray-400 cursor-not-allowed'\n                  : 'text-gray-700 hover:bg-gray-100'\n                }\n              `}\n            >\n              <TbChevronLeft className=\"w-5 h-5\" />\n              Previous\n            </button>\n\n            {/* Question Numbers */}\n            <div className=\"flex items-center gap-2 overflow-x-auto max-w-md\">\n              {questions && Array.isArray(questions) ? questions.map((question, index) => {\n                // Safety check for question object\n                if (!question || typeof question !== 'object') {\n                  console.warn('Invalid question at index:', index, question);\n                  return null;\n                }\n\n                return (\n                  <button\n                    key={question._id || index}\n                    onClick={() => goToQuestion(index)}\n                    className={`\n                      w-8 h-8 rounded-full text-sm font-medium transition-colors flex-shrink-0\n                      ${index === currentQuestionIndex\n                        ? 'bg-blue-500 text-white'\n                        : answers[question._id]\n                        ? 'bg-green-100 text-green-600'\n                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                      }\n                    `}\n                  >\n                    {index + 1}\n                  </button>\n                );\n              }) : null}\n            </div>\n\n            {/* Next/Submit Button */}\n            {currentQuestionIndex === totalQuestions - 1 ? (\n              <button\n                onClick={() => setShowSubmitConfirm(true)}\n                className=\"flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors\"\n              >\n                <TbCheck className=\"w-5 h-5\" />\n                Submit Quiz\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Next\n                <TbChevronRight className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Submit Confirmation Modal */}\n      <AnimatePresence>\n        {showSubmitConfirm && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\"\n          >\n            <motion.div\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0.9, opacity: 0 }}\n              className=\"bg-white rounded-xl p-6 max-w-md w-full\"\n            >\n              <h3 className=\"text-lg font-bold text-gray-900 mb-4\">Submit Quiz?</h3>\n              <p className=\"text-gray-600 mb-6\">\n                Are you sure you want to submit your quiz? You won't be able to change your answers after submission.\n              </p>\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={() => setShowSubmitConfirm(false)}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSubmit}\n                  className=\"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  Submit\n                </button>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default QuizInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,aAAa,QACR,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,aAAa,GAAGA,CAAC;EACrBC,IAAI;EACJC,SAAS,GAAG,EAAE;EACdC,QAAQ;EACRC,MAAM;EACNC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAAe,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,QAAQ,KAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACjF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM8B,eAAe,GAAGd,SAAS,CAACK,oBAAoB,CAAC;EACvD,MAAMU,cAAc,GAAGf,SAAS,CAACgB,MAAM;EACvC,MAAMC,QAAQ,GAAI,CAACZ,oBAAoB,GAAG,CAAC,IAAIU,cAAc,GAAI,GAAG;;EAEpE;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIwB,aAAa,IAAI,CAAC,EAAE;MACtBS,YAAY,CAAC,CAAC;MACd;IACF;IAEA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BV,gBAAgB,CAACW,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACV,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMc,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAQ,GAAEC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,IAAGF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EAClF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,MAAM,KAAK;IACjDzB,UAAU,CAACa,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACW,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI7B,oBAAoB,GAAGU,cAAc,GAAG,CAAC,EAAE;MAC7CT,uBAAuB,CAACe,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI9B,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACe,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMe,YAAY,GAAIC,KAAK,IAAK;IAC9B/B,uBAAuB,CAAC+B,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMnB,YAAY,GAAGA,CAAA,KAAM;IACzBjB,QAAQ,IAAIA,QAAQ,CAACM,OAAO,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM+B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACxB,eAAe,EAAE;MACpB,oBACEjB,OAAA;QAAKM,SAAS,EAAC,gCAAgC;QAAAoC,QAAA,gBAC7C1C,OAAA,CAACF,aAAa;UAACQ,SAAS,EAAC;QAAwB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpD9C,OAAA;UAAA0C,QAAA,EAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAEV;;IAEA;IACAC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE/B,eAAe,CAAC;IACjD8B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE/B,eAAe,CAACgC,OAAO,CAAC;IACzDF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE/B,eAAe,CAACiC,IAAI,IAAIjC,eAAe,CAACkC,UAAU,CAAC;IAEjF,QAAQlC,eAAe,CAACiC,IAAI,IAAIjC,eAAe,CAACkC,UAAU;MACxD,KAAK,iBAAiB;MACtB,KAAK,KAAK;QACR;QACA,MAAMF,OAAO,GAAGG,KAAK,CAACC,OAAO,CAACpC,eAAe,CAACgC,OAAO,CAAC,GAAGhC,eAAe,CAACgC,OAAO,GAAG,EAAE;QAErF,IAAIA,OAAO,CAAC9B,MAAM,KAAK,CAAC,EAAE;UACxB,oBACEnB,OAAA;YAAKM,SAAS,EAAC,gCAAgC;YAAAoC,QAAA,gBAC7C1C,OAAA,CAACF,aAAa;cAACQ,SAAS,EAAC;YAAwB;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD9C,OAAA;cAAA0C,QAAA,EAAG;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAEV;QAEA,oBACE9C,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAoC,QAAA,EACvBO,OAAO,CAACK,GAAG,CAAC,CAACC,MAAM,EAAEf,KAAK,KAAK;YAC9B,MAAMgB,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGlB,KAAK,CAAC,CAAC,CAAC;YACtD,MAAMmB,UAAU,GAAGjD,OAAO,CAACO,eAAe,CAAC2C,GAAG,CAAC,KAAKL,MAAM;YAE1D,oBACEvD,OAAA,CAACV,MAAM,CAACuE,MAAM;cAEZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BE,OAAO,EAAEA,CAAA,KAAM/B,kBAAkB,CAACjB,eAAe,CAAC2C,GAAG,EAAEL,MAAM,CAAE;cAC/DjD,SAAS,EAAG;AAC9B;AACA,sBAAsBqD,UAAU,GACR,0CAA0C,GAC1C,iEACH;AACrB,mBAAoB;cAAAjB,QAAA,eAEF1C,OAAA;gBAAKM,SAAS,EAAC,yBAAyB;gBAAAoC,QAAA,gBACtC1C,OAAA;kBAAKM,SAAS,EAAG;AACrC;AACA,wBAAwBqD,UAAU,GACR,wCAAwC,GACxC,+BACH;AACvB,qBAAsB;kBAAAjB,QAAA,EACCc;gBAAY;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN9C,OAAA;kBAAMM,SAAS,EAAC,QAAQ;kBAAAoC,QAAA,EAAEa;gBAAM;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC,GAvBDN,KAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBG,CAAC;UAEpB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAGV,KAAK,mBAAmB;MACxB,KAAK,MAAM;QACT,oBACE9C,OAAA;UAAA0C,QAAA,eACE1C,OAAA;YACEkE,KAAK,EAAExD,OAAO,CAACO,eAAe,CAAC2C,GAAG,CAAC,IAAI,EAAG;YAC1CO,QAAQ,EAAGC,CAAC,IAAKlC,kBAAkB,CAACjB,eAAe,CAAC2C,GAAG,EAAEQ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACzEI,WAAW,EAAC,0BAA0B;YACtChE,SAAS,EAAC,mHAAmH;YAC7HiE,IAAI,EAAE;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAGV,KAAK,OAAO;QACV;QACA,MAAM0B,YAAY,GAAGpB,KAAK,CAACC,OAAO,CAACpC,eAAe,CAACgC,OAAO,CAAC,GAAGhC,eAAe,CAACgC,OAAO,GAAG,EAAE;QAE1F,oBACEjD,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAoC,QAAA,GACvBzB,eAAe,CAACwD,QAAQ,iBACvBzE,OAAA;YAAKM,SAAS,EAAC,4BAA4B;YAAAoC,QAAA,eACzC1C,OAAA;cACE0E,GAAG,EAAEzD,eAAe,CAACwD,QAAS;cAC9BE,GAAG,EAAC,UAAU;cACdrE,SAAS,EAAC;YAAsC;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAEA0B,YAAY,CAACrD,MAAM,KAAK,CAAC,gBACxBnB,OAAA;YAAKM,SAAS,EAAC,gCAAgC;YAAAoC,QAAA,gBAC7C1C,OAAA,CAACF,aAAa;cAACQ,SAAS,EAAC;YAAwB;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD9C,OAAA;cAAA0C,QAAA,EAAG;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,gBAEN9C,OAAA;YAAKM,SAAS,EAAC,WAAW;YAAAoC,QAAA,EACvB8B,YAAY,CAAClB,GAAG,CAAC,CAACC,MAAM,EAAEf,KAAK,KAAK;cACnC,MAAMgB,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGlB,KAAK,CAAC;cACpD,MAAMmB,UAAU,GAAGjD,OAAO,CAACO,eAAe,CAAC2C,GAAG,CAAC,KAAKL,MAAM;cAE1D,oBACEvD,OAAA;gBAEEiE,OAAO,EAAEA,CAAA,KAAM/B,kBAAkB,CAACjB,eAAe,CAAC2C,GAAG,EAAEL,MAAM,CAAE;gBAC/DjD,SAAS,EAAG;AAClC;AACA,0BAA0BqD,UAAU,GACR,0CAA0C,GAC1C,gDACH;AACzB,uBAAwB;gBAAAjB,QAAA,eAEF1C,OAAA;kBAAKM,SAAS,EAAC,yBAAyB;kBAAAoC,QAAA,gBACtC1C,OAAA;oBAAKM,SAAS,EAAG;AACzC;AACA,4BAA4BqD,UAAU,GACR,wCAAwC,GACxC,+BACH;AAC3B,yBAA0B;oBAAAjB,QAAA,EACCc;kBAAY;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACN9C,OAAA;oBAAA0C,QAAA,EAAOa;kBAAM;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cAAC,GArBDN,KAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBJ,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV;QACE,oBACE9C,OAAA;UAAKM,SAAS,EAAC,gCAAgC;UAAAoC,QAAA,gBAC7C1C,OAAA,CAACF,aAAa;YAACQ,SAAS,EAAC;UAAwB;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpD9C,OAAA;YAAA0C,QAAA,GAAG,6BAA2B,EAACzB,eAAe,CAACiC,IAAI,IAAIjC,eAAe,CAACkC,UAAU;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC;IAEZ;EACF,CAAC;EAED,oBACE9C,OAAA;IAAKM,SAAS,EAAG,yCAAwCA,SAAU,EAAE;IAAAoC,QAAA,gBAEnE1C,OAAA;MAAKM,SAAS,EAAC,+CAA+C;MAAAoC,QAAA,eAC5D1C,OAAA;QAAKM,SAAS,EAAC,6BAA6B;QAAAoC,QAAA,gBAC1C1C,OAAA;UAAKM,SAAS,EAAC,mCAAmC;UAAAoC,QAAA,gBAEhD1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAIM,SAAS,EAAC,iCAAiC;cAAAoC,QAAA,EAAE,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,IAAI,KAAI;YAAM;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3E9C,OAAA;cAAGM,SAAS,EAAC,uBAAuB;cAAAoC,QAAA,EAAE,CAAAxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,OAAO,KAAI;YAAS;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAGN9C,OAAA;YAAKM,SAAS,EAAG;AAC7B;AACA,gBAAgBM,aAAa,IAAI,GAAG,GAAG,yBAAyB,GAAG,2BAA4B;AAC/F,aAAc;YAAA8B,QAAA,gBACA1C,OAAA,CAACR,OAAO;cAACc,SAAS,EAAC;YAAS;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC9BpB,UAAU,CAACd,aAAa,CAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9C,OAAA;UAAKM,SAAS,EAAC,MAAM;UAAAoC,QAAA,gBACnB1C,OAAA;YAAKM,SAAS,EAAC,8DAA8D;YAAAoC,QAAA,gBAC3E1C,OAAA;cAAA0C,QAAA,GAAM,WAAS,EAAClC,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACU,cAAc;YAAA;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpE9C,OAAA;cAAA0C,QAAA,GAAOb,IAAI,CAACiD,KAAK,CAAC1D,QAAQ,CAAC,EAAC,YAAU;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN9C,OAAA;YAAKM,SAAS,EAAC,qCAAqC;YAAAoC,QAAA,eAClD1C,OAAA,CAACV,MAAM,CAACyF,GAAG;cACTzE,SAAS,EAAC,8BAA8B;cACxC0E,OAAO,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAG,GAAE7D,QAAS;cAAG,CAAE;cACnC+D,UAAU,EAAE;gBAAErE,QAAQ,EAAE;cAAI;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA;MAAKM,SAAS,EAAC,2CAA2C;MAAAoC,QAAA,eACxD1C,OAAA,CAACT,eAAe;QAAC6F,IAAI,EAAC,MAAM;QAAA1C,QAAA,eAC1B1C,OAAA,CAACV,MAAM,CAACyF,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BH,UAAU,EAAE;YAAErE,QAAQ,EAAE;UAAI,CAAE;UAC9BR,SAAS,EAAC,iDAAiD;UAAAoC,QAAA,gBAG3D1C,OAAA;YAAKM,SAAS,EAAC,MAAM;YAAAoC,QAAA,gBACnB1C,OAAA;cAAIM,SAAS,EAAC,sEAAsE;cAAAoC,QAAA,EACjF,CAAAzB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2D,IAAI,MAAI3D,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuE,QAAQ,KAAI;YAAwB;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,EAEJ,CAAA7B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwE,WAAW,kBAC3BzF,OAAA;cAAGM,SAAS,EAAC,oBAAoB;cAAAoC,QAAA,EAAEzB,eAAe,CAACwE;YAAW;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACnE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLL,cAAc,CAAC,CAAC;QAAA,GAnBZjC,oBAAoB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAGN9C,OAAA;MAAKM,SAAS,EAAC,mCAAmC;MAAAoC,QAAA,eAChD1C,OAAA;QAAKM,SAAS,EAAC,6BAA6B;QAAAoC,QAAA,eAC1C1C,OAAA;UAAKM,SAAS,EAAC,mCAAmC;UAAAoC,QAAA,gBAEhD1C,OAAA;YACEiE,OAAO,EAAE3B,YAAa;YACtBoD,QAAQ,EAAElF,oBAAoB,KAAK,CAAE;YACrCF,SAAS,EAAG;AAC1B;AACA,kBAAkBE,oBAAoB,KAAK,CAAC,GACxB,kCAAkC,GAClC,iCACH;AACjB,eAAgB;YAAAkC,QAAA,gBAEF1C,OAAA,CAACP,aAAa;cAACa,SAAS,EAAC;YAAS;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT9C,OAAA;YAAKM,SAAS,EAAC,kDAAkD;YAAAoC,QAAA,EAC9DvC,SAAS,IAAIiD,KAAK,CAACC,OAAO,CAAClD,SAAS,CAAC,GAAGA,SAAS,CAACmD,GAAG,CAAC,CAACkC,QAAQ,EAAEhD,KAAK,KAAK;cAC1E;cACA,IAAI,CAACgD,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;gBAC7CzC,OAAO,CAAC4C,IAAI,CAAC,4BAA4B,EAAEnD,KAAK,EAAEgD,QAAQ,CAAC;gBAC3D,OAAO,IAAI;cACb;cAEA,oBACExF,OAAA;gBAEEiE,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAACC,KAAK,CAAE;gBACnClC,SAAS,EAAG;AAChC;AACA,wBAAwBkC,KAAK,KAAKhC,oBAAoB,GAC5B,wBAAwB,GACxBE,OAAO,CAAC8E,QAAQ,CAAC5B,GAAG,CAAC,GACrB,6BAA6B,GAC7B,6CACH;AACvB,qBAAsB;gBAAAlB,QAAA,EAEDF,KAAK,GAAG;cAAC,GAZLgD,QAAQ,CAAC5B,GAAG,IAAIpB,KAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAapB,CAAC;YAEb,CAAC,CAAC,GAAG;UAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGLtC,oBAAoB,KAAKU,cAAc,GAAG,CAAC,gBAC1ClB,OAAA;YACEiE,OAAO,EAAEA,CAAA,KAAMjD,oBAAoB,CAAC,IAAI,CAAE;YAC1CV,SAAS,EAAC,uHAAuH;YAAAoC,QAAA,gBAEjI1C,OAAA,CAACL,OAAO;cAACW,SAAS,EAAC;YAAS;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET9C,OAAA;YACEiE,OAAO,EAAE5B,QAAS;YAClB/B,SAAS,EAAC,qHAAqH;YAAAoC,QAAA,GAChI,MAEC,eAAA1C,OAAA,CAACN,cAAc;cAACY,SAAS,EAAC;YAAS;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA,CAACT,eAAe;MAAAmD,QAAA,EACb3B,iBAAiB,iBAChBf,OAAA,CAACV,MAAM,CAACyF,GAAG;QACTC,OAAO,EAAE;UAAEK,OAAO,EAAE;QAAE,CAAE;QACxBH,OAAO,EAAE;UAAEG,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrB/E,SAAS,EAAC,gFAAgF;QAAAoC,QAAA,eAE1F1C,OAAA,CAACV,MAAM,CAACyF,GAAG;UACTC,OAAO,EAAE;YAAEjB,KAAK,EAAE,GAAG;YAAEsB,OAAO,EAAE;UAAE,CAAE;UACpCH,OAAO,EAAE;YAAEnB,KAAK,EAAE,CAAC;YAAEsB,OAAO,EAAE;UAAE,CAAE;UAClCE,IAAI,EAAE;YAAExB,KAAK,EAAE,GAAG;YAAEsB,OAAO,EAAE;UAAE,CAAE;UACjC/E,SAAS,EAAC,yCAAyC;UAAAoC,QAAA,gBAEnD1C,OAAA;YAAIM,SAAS,EAAC,sCAAsC;YAAAoC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtE9C,OAAA;YAAGM,SAAS,EAAC,oBAAoB;YAAAoC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9C,OAAA;YAAKM,SAAS,EAAC,YAAY;YAAAoC,QAAA,gBACzB1C,OAAA;cACEiE,OAAO,EAAEA,CAAA,KAAMjD,oBAAoB,CAAC,KAAK,CAAE;cAC3CV,SAAS,EAAC,qGAAqG;cAAAoC,QAAA,EAChH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9C,OAAA;cACEiE,OAAO,EAAE5C,YAAa;cACtBf,SAAS,EAAC,0FAA0F;cAAAoC,QAAA,EACrG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAACvC,EAAA,CA7YIN,aAAa;AAAA2F,EAAA,GAAb3F,aAAa;AA+YnB,eAAeA,aAAa;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}