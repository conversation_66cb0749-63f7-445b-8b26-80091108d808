{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport { TbCheck, TbX, TbTrophy, TbBrain, TbTarget, TbRefresh, TbBulb } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { getQuizResult } from '../../../apicalls/quiz';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  var _location$state, _result$correctAnswer, _result$wrongAnswers;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [result, setResult] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const resultFromState = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.result;\n\n  // Debug result data\n  console.log('🎯 Quiz Result Page - Result data:', result);\n  console.log('💰 XP Data in result:', result === null || result === void 0 ? void 0 : result.xpData);\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        var _examResponse$data;\n        setLoading(true);\n        dispatch(ShowLoading());\n\n        // Fetch exam data\n        const examResponse = await getExamById({\n          examId: id\n        });\n        if (!examResponse.success) {\n          message.error(examResponse.message);\n          navigate('/user/quiz');\n          return;\n        }\n        setExamData(examResponse.data);\n        setQuestions(((_examResponse$data = examResponse.data) === null || _examResponse$data === void 0 ? void 0 : _examResponse$data.questions) || []);\n\n        // If result is provided via state, use it\n        if (resultFromState) {\n          setResult(resultFromState);\n        } else {\n          // Otherwise, fetch the latest result for this quiz\n          const resultResponse = await getQuizResult(id);\n          if (resultResponse.success) {\n            var _report$correctAnswer, _report$correctAnswer2, _report$wrongAnswers;\n            // Transform the report data to match expected result format\n            const report = resultResponse.data;\n            const transformedResult = {\n              correctAnswers: report.correctAnswers || [],\n              wrongAnswers: report.wrongAnswers || [],\n              verdict: report.verdict,\n              score: report.percentage,\n              points: report.points || ((_report$correctAnswer = report.correctAnswers) === null || _report$correctAnswer === void 0 ? void 0 : _report$correctAnswer.length) * 10 || 0,\n              totalQuestions: (((_report$correctAnswer2 = report.correctAnswers) === null || _report$correctAnswer2 === void 0 ? void 0 : _report$correctAnswer2.length) || 0) + (((_report$wrongAnswers = report.wrongAnswers) === null || _report$wrongAnswers === void 0 ? void 0 : _report$wrongAnswers.length) || 0),\n              timeSpent: report.timeSpent || 0,\n              totalTimeAllowed: report.totalTimeAllowed || 0,\n              xpData: report.xpData\n            };\n            setResult(transformedResult);\n          } else {\n            message.error('No quiz result found. Please take the quiz first.');\n            navigate('/user/quiz');\n            return;\n          }\n        }\n      } catch (error) {\n        message.error(error.message);\n        navigate('/user/quiz');\n      } finally {\n        setLoading(false);\n        dispatch(HideLoading());\n      }\n    };\n    if (id) {\n      fetchData();\n    }\n  }, [id, dispatch, navigate, resultFromState]);\n\n  // Play sound effect based on performance\n  useEffect(() => {\n    if (result) {\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n\n      // Play performance-based sound\n      const playSound = () => {\n        try {\n          const score = result.score || 0;\n\n          // Create enhanced sound effects using Web Audio API\n          const createEnhancedSound = (frequencies, durations, volumes = [0.3], types = ['sine'], staggerDelay = 0.15) => {\n            try {\n              const audioContext = new window.AudioContext();\n              frequencies.forEach((frequency, index) => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n                const delay = index * staggerDelay; // Customizable stagger timing\n\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + delay);\n                oscillator.type = types[index] || types[0] || 'sine';\n                const volume = volumes[index] || volumes[0] || 0.3;\n                const duration = durations[index] || durations[0] || 0.5;\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + delay + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + delay + duration);\n                oscillator.start(audioContext.currentTime + delay);\n                oscillator.stop(audioContext.currentTime + delay + duration);\n              });\n              return true;\n            } catch (error) {\n              console.log('Enhanced Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create chord sound (simultaneous notes) for richer harmonies\n          const createChordSound = (frequencies, duration, volume, type = 'sine') => {\n            try {\n              const audioContext = new window.AudioContext();\n              frequencies.forEach(frequency => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n                oscillator.type = type;\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n                oscillator.start(audioContext.currentTime);\n                oscillator.stop(audioContext.currentTime + duration);\n              });\n              return true;\n            } catch (error) {\n              console.log('Chord Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create triumphant celebration sound for perfect scores\n          const createCelebrationSound = () => {\n            const frequencies = [523, 659, 784, 1047, 1319]; // C5, E5, G5, C6, E6 - Major chord progression\n            const durations = [0.25, 0.25, 0.25, 0.4, 0.6];\n            const volumes = [0.45, 0.45, 0.45, 0.5, 0.55];\n            const types = ['sine', 'triangle', 'sine', 'triangle', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create excellent achievement sound - bright chord followed by ascending notes\n          const createExcellentSound = () => {\n            // Play a bright major chord first\n            createChordSound([440, 554, 659], 0.4, 0.3, 'triangle'); // A4, C#5, E5\n            // Follow with ascending melody\n            setTimeout(() => {\n              const frequencies = [659, 784]; // E5, G5\n              const durations = [0.3, 0.4];\n              const volumes = [0.35, 0.4];\n              const types = ['sine', 'sine'];\n              createEnhancedSound(frequencies, durations, volumes, types, 0.2);\n            }, 300);\n            return true;\n          };\n\n          // Create encouraging pass sound - warm and positive\n          const createPassSound = () => {\n            const frequencies = [349, 440]; // F4, A4 - Simple positive interval\n            const durations = [0.4, 0.5];\n            const volumes = [0.35, 0.4];\n            const types = ['sine', 'triangle'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create gentle encouragement sound for fails - supportive, not harsh\n          const createFailSound = () => {\n            const frequencies = [262, 294]; // C4, D4 - Gentle, hopeful interval\n            const durations = [0.5, 0.7];\n            const volumes = [0.3, 0.25];\n            const types = ['sine', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Play distinct sounds based on performance with unique characteristics\n          if (score === 100) {\n            // Perfect score - triumphant celebration with 5-note ascending sequence\n            createCelebrationSound();\n            // Add extra celebratory beeps\n            setTimeout(() => {\n              const frequencies = [1047, 1319, 1568]; // C6, E6, G6\n              const durations = [0.2, 0.2, 0.4];\n              const volumes = [0.3, 0.3, 0.35];\n              const types = ['triangle', 'triangle', 'triangle'];\n              createEnhancedSound(frequencies, durations, volumes, types);\n            }, 800);\n            console.log('🏆 PERFECT SCORE! 🎉');\n          } else if (score >= 80) {\n            // Excellent - bright 3-note major chord\n            createExcellentSound();\n            console.log('🎉 EXCELLENT! ⭐');\n          } else if (result.verdict === \"Pass\") {\n            // Pass - warm 2-note positive interval\n            createPassSound();\n            console.log('✅ Well Done! 🚀');\n          } else {\n            // Fail - gentle 2-note supportive tone (different from pass)\n            createFailSound();\n            // Add a gentle follow-up note for encouragement\n            setTimeout(() => {\n              const frequencies = [330]; // E4 - hopeful note\n              const durations = [0.6];\n              const volumes = [0.25];\n              const types = ['sine'];\n              createEnhancedSound(frequencies, durations, volumes, types);\n            }, 600);\n            console.log('💪 Keep Trying! 🌱');\n          }\n        } catch (error) {\n          console.log('Audio not supported:', error);\n          // Visual feedback as fallback\n          if (result.verdict === \"Pass\") {\n            console.log('🎉 Quiz Passed!');\n          } else {\n            console.log('💪 Keep trying!');\n          }\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playSound, 500);\n    }\n  }, [result]);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [String(question)]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  // Handle loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-700 mb-2\",\n          children: \"Loading Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Please wait while we fetch your quiz results...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Handle missing result data\n  if (!result) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-700 mb-2\",\n          children: \"No Result Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-4\",\n          children: \"Unable to load quiz results.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/user/quiz'),\n          className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Calculate performance level for animations and sounds\n  const getPerformanceLevel = () => {\n    const score = result.score || 0;\n    if (score === 100) return 'perfect';\n    if (score >= 80) return 'excellent';\n    if (score >= 60) return 'good';\n    if (result.verdict === \"Pass\") return 'pass';\n    return 'fail';\n  };\n  const performanceLevel = getPerformanceLevel();\n\n  // Performance-based styling and content\n  const getPerformanceConfig = () => {\n    switch (performanceLevel) {\n      case 'perfect':\n        return {\n          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',\n          iconBg: 'from-yellow-400 to-orange-500',\n          icon: TbTrophy,\n          title: '🏆 PERFECT SCORE!',\n          subtitle: 'Outstanding! You\\'re a quiz master! 🌟',\n          confetti: true,\n          soundFile: '/sounds/perfect.mp3'\n        };\n      case 'excellent':\n        return {\n          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',\n          iconBg: 'from-green-400 to-emerald-500',\n          icon: TbTrophy,\n          title: '🎉 EXCELLENT!',\n          subtitle: 'Amazing work! You\\'re doing great! ✨',\n          confetti: true,\n          soundFile: '/sounds/excellent.mp3'\n        };\n      case 'good':\n      case 'pass':\n        return {\n          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',\n          iconBg: 'from-blue-400 to-indigo-500',\n          icon: TbCheck,\n          title: '✅ Well Done!',\n          subtitle: 'Good job! Keep up the great work! 🚀',\n          confetti: result.verdict === \"Pass\",\n          soundFile: '/sounds/pass.mp3'\n        };\n      default:\n        return {\n          bgGradient: 'from-red-400 via-pink-500 to-rose-600',\n          iconBg: 'from-red-400 to-pink-500',\n          icon: TbX,\n          title: '💪 Keep Trying!',\n          subtitle: 'Don\\'t give up! Practice makes perfect! 🌱',\n          confetti: false,\n          soundFile: '/sounds/fail.mp3'\n        };\n    }\n  };\n  const config = getPerformanceConfig();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\",\n    children: [config.confetti && /*#__PURE__*/_jsxDEV(Confetti, {\n      width: width,\n      height: height\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 27\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 lg:w-40 lg:h-40 bg-gradient-to-br ${config.iconBg} rounded-full mb-6 sm:mb-8 shadow-2xl relative overflow-hidden`,\n              style: {\n                animation: performanceLevel === 'perfect' ? 'megaBounce 1.2s infinite, rainbowGlow 3s infinite, rotate360 4s infinite linear' : performanceLevel === 'excellent' ? 'bigBounce 1s infinite, excellentGlow 2.5s infinite' : performanceLevel === 'pass' || performanceLevel === 'good' ? 'gentlePulse 2s infinite, passGlow 3s infinite' : 'encourageShake 0.8s ease-in-out 3, failGlow 2s infinite',\n                transform: 'scale(1)',\n                transformOrigin: 'center'\n              },\n              children: [performanceLevel === 'perfect' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-full\",\n                  style: {\n                    background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',\n                    animation: 'sparkle 1.5s infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-2 right-2 w-3 h-3 bg-white rounded-full\",\n                  style: {\n                    animation: 'twinkle 1s infinite alternate'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-3 left-3 w-2 h-2 bg-yellow-300 rounded-full\",\n                  style: {\n                    animation: 'twinkle 1.2s infinite alternate-reverse'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(config.icon, {\n                className: \"w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 text-white relative z-10\",\n                style: {\n                  filter: performanceLevel === 'perfect' ? 'drop-shadow(0 0 10px rgba(255,255,255,0.8))' : 'none'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl sm:text-4xl lg:text-6xl font-black mb-4 sm:mb-6 relative\",\n              style: {\n                animation: performanceLevel === 'perfect' ? 'titleBounce 1.5s infinite, rainbowText 4s infinite' : performanceLevel === 'excellent' ? 'titlePulse 2s infinite, excellentText 3s infinite' : performanceLevel === 'pass' || performanceLevel === 'good' ? 'titleFadeIn 1s ease-out, passText 3s infinite' : 'titleShake 0.6s ease-in-out 2, failText 2s infinite',\n                color: 'white',\n                textShadow: performanceLevel === 'perfect' ? '3px 3px 6px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.6)' : performanceLevel === 'excellent' ? '2px 2px 4px rgba(0,0,0,0.8), 0 0 15px rgba(255,255,255,0.4)' : '2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5)',\n                transformOrigin: 'center'\n              },\n              children: [config.title, performanceLevel === 'perfect' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-0 left-1/4 w-1 h-1 bg-yellow-300 rounded-full\",\n                  style: {\n                    animation: 'float 3s infinite ease-in-out'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-1/4 right-1/4 w-1 h-1 bg-white rounded-full\",\n                  style: {\n                    animation: 'float 2.5s infinite ease-in-out 0.5s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute bottom-1/4 left-1/3 w-1 h-1 bg-yellow-400 rounded-full\",\n                  style: {\n                    animation: 'float 3.5s infinite ease-in-out 1s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-base sm:text-lg lg:text-xl font-medium px-4 py-2 rounded-lg\",\n              style: {\n                color: 'white',\n                textShadow: '1px 1px 3px rgba(0,0,0,0.8)',\n                backgroundColor: 'rgba(0,0,0,0.3)',\n                backdropFilter: 'blur(10px)'\n              },\n              children: config.subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2\",\n              children: [result.score || 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2\",\n              children: ((_result$correctAnswer = result.correctAnswers) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Correct\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2\",\n              children: ((_result$wrongAnswers = result.wrongAnswers) === null || _result$wrongAnswers === void 0 ? void 0 : _result$wrongAnswers.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Wrong\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2\",\n              children: [Math.floor((result.timeSpent || 0) / 60), \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\",\n              children: \"Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this), result.xpData ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8 lg:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(XPResultDisplay, {\n            xpData: result.xpData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 13\n        }, this) :\n        /*#__PURE__*/\n        /* Fallback XP Display */\n        _jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8 lg:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-6 border-2 border-yellow-200 shadow-lg text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n                className: \"w-8 h-8 text-yellow-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-800\",\n                children: \"XP Earned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl font-bold text-yellow-600 mb-2\",\n              children: [\"+\", result.points || result.score * 10 || 100]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Great job completing this quiz!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 sm:mb-8 lg:mb-12\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-6 sm:mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2\",\n                children: \"\\uD83D\\uDCDA Learning Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm sm:text-base\",\n                children: \"Review your answers and learn from explanations to improve your understanding\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6 sm:space-y-8\",\n              children: questions.map((question, index) => {\n                var _result$correctAnswer2, _result$wrongAnswers$;\n                // Safety check to prevent rendering objects\n                if (!question || typeof question !== 'object' || !question._id) {\n                  console.warn('Invalid question object at index:', index, question);\n                  return null;\n                }\n                const userAnswer = ((_result$correctAnswer2 = result.correctAnswers.find(q => q._id === question._id)) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.userAnswer) || ((_result$wrongAnswers$ = result.wrongAnswers.find(q => q._id === question._id)) === null || _result$wrongAnswers$ === void 0 ? void 0 : _result$wrongAnswers$.userAnswer) || \"\";\n                const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n                const correctAnswer = question.correctAnswer || question.correctOption || 'N/A';\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-xl sm:rounded-2xl p-5 sm:p-6 lg:p-8 border-2 transition-all duration-300 ${isCorrect ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 border-green-400 shadow-green-200' : 'bg-gradient-to-br from-red-50 via-pink-50 to-red-100 border-red-400 shadow-red-200'} shadow-lg hover:shadow-xl hover:scale-[1.02]`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-4 sm:mb-6\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 sm:gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-full font-bold text-white text-base sm:text-lg shadow-lg ${isCorrect ? 'bg-gradient-to-br from-green-500 to-emerald-600' : 'bg-gradient-to-br from-red-500 to-pink-600'}`,\n                        children: index + 1\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 585,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"text-lg sm:text-xl font-bold text-gray-900\",\n                          children: [\"Question \", index + 1]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 591,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 590,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `flex items-center gap-2 px-4 sm:px-5 py-2 sm:py-3 rounded-full font-bold text-sm sm:text-base shadow-md ${isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                      children: isCorrect ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 604,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Correct\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 605,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 609,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Incorrect\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 610,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-5 sm:mb-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `p-5 sm:p-6 rounded-xl border-3 shadow-md ${isCorrect ? 'bg-white border-green-500' : 'bg-white border-red-500'}`,\n                      style: {\n                        backgroundColor: '#ffffff',\n                        border: isCorrect ? '3px solid #22c55e' : '3px solid #ef4444',\n                        boxShadow: isCorrect ? '0 4px 15px rgba(34, 197, 94, 0.2)' : '0 4px 15px rgba(239, 68, 68, 0.2)'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-gray-900 text-base sm:text-lg leading-relaxed font-bold\",\n                        style: {\n                          color: '#111827',\n                          fontWeight: '700',\n                          fontSize: '1.1rem',\n                          lineHeight: '1.7'\n                        },\n                        children: String(question.name || 'Question')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 618,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 23\n                  }, this), question.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-5 sm:mb-6 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"inline-block p-3 sm:p-4 bg-white rounded-lg border-2 border-gray-200 shadow-sm\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: question.image,\n                        alt: \"Question Reference\",\n                        className: \"max-w-full max-h-40 sm:max-h-56 rounded-lg\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 642,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4 sm:space-y-5\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-blue-600\",\n                          children: \"\\uD83D\\uDC64\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 656,\n                          columnNumber: 29\n                        }, this), \"Your Answer:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 655,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `flex items-start gap-3 sm:gap-4 p-5 sm:p-6 rounded-xl font-bold text-base sm:text-lg border-3 shadow-md ${isCorrect ? 'bg-green-50 text-green-900 border-green-500' : 'bg-red-50 text-red-900 border-red-500'}`,\n                        style: {\n                          border: isCorrect ? '3px solid #22c55e' : '3px solid #ef4444',\n                          boxShadow: isCorrect ? '0 4px 15px rgba(34, 197, 94, 0.2)' : '0 4px 15px rgba(239, 68, 68, 0.2)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 mt-1\",\n                          children: isCorrect ? /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 bg-green-500 rounded-full shadow-lg\",\n                            children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                              className: \"w-5 h-5 sm:w-6 sm:h-6 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 670,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 669,\n                            columnNumber: 33\n                          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 bg-red-500 rounded-full shadow-lg\",\n                            children: /*#__PURE__*/_jsxDEV(TbX, {\n                              className: \"w-5 h-5 sm:w-6 sm:h-6 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 674,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 673,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 667,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-gray-900 font-bold text-lg\",\n                            style: {\n                              color: '#111827',\n                              fontWeight: '700',\n                              fontSize: '1.1rem'\n                            },\n                            children: String(userAnswer || 'No answer provided')\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 679,\n                            columnNumber: 31\n                          }, this), isCorrect && /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-green-800 text-sm sm:text-base mt-2 font-bold\",\n                            children: \"\\uD83C\\uDF89 Excellent! This is the correct answer.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 687,\n                            columnNumber: 33\n                          }, this), !isCorrect && userAnswer && /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-red-800 text-sm sm:text-base mt-2 font-bold\",\n                            children: \"\\u274C This answer is incorrect. Let's learn why below.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 692,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 678,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 659,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-green-600\",\n                          children: \"\\u2705\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 703,\n                          columnNumber: 29\n                        }, this), \"Correct Answer:\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 702,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-start gap-3 sm:gap-4 p-5 sm:p-6 bg-green-50 text-green-900 rounded-xl font-bold text-base sm:text-lg border-3 border-green-500 shadow-md\",\n                        style: {\n                          border: '3px solid #22c55e',\n                          boxShadow: '0 4px 15px rgba(34, 197, 94, 0.2)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-shrink-0 mt-1\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 bg-green-500 rounded-full shadow-lg\",\n                            children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                              className: \"w-5 h-5 sm:w-6 sm:h-6 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 712,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 711,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 710,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-gray-900 font-bold text-lg\",\n                            style: {\n                              color: '#111827',\n                              fontWeight: '700',\n                              fontSize: '1.1rem'\n                            },\n                            children: String(correctAnswer || 'N/A')\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 716,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-green-800 text-sm sm:text-base mt-2 font-bold\",\n                            children: \"\\uD83D\\uDCA1 Remember this answer for future reference!\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 723,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 715,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 706,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 25\n                    }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-4 sm:mt-5\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => fetchExplanation(String(question.name || 'Question'), String(correctAnswer || 'N/A'), String(userAnswer || ''), question.image),\n                        className: \"w-full flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl\",\n                        children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n                          className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 742,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Get Explanation\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 743,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 29\n                      }, this), explanations[String(question.name || 'Question')] && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-4 p-5 sm:p-6 bg-blue-50 border-3 border-blue-400 rounded-xl shadow-md\",\n                        style: {\n                          border: '3px solid #3b82f6',\n                          boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-start gap-3 mb-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-center w-10 h-10 bg-blue-500 rounded-full flex-shrink-0 shadow-lg\",\n                            children: /*#__PURE__*/_jsxDEV(TbBulb, {\n                              className: \"w-6 h-6 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 753,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 752,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"font-bold text-blue-900 text-lg sm:text-xl\",\n                            children: \"Explanation:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 755,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 751,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-gray-900 text-base sm:text-lg leading-relaxed font-medium\",\n                          style: {\n                            color: '#111827',\n                            fontWeight: '600',\n                            lineHeight: '1.7'\n                          },\n                          children: String(explanations[String(question.name || 'Question')] || '')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 757,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 747,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 27\n                    }, this), isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-4 p-4 bg-green-50 border-2 border-green-200 rounded-lg\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center justify-center w-8 h-8 bg-green-500 rounded-full\",\n                          children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                            className: \"w-5 h-5 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 774,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 773,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"font-bold text-green-900 text-sm sm:text-base\",\n                            children: \"Great job!\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 777,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-green-700 text-xs sm:text-sm\",\n                            children: \"You demonstrated good understanding of this concept. Keep it up! \\uD83C\\uDF1F\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 778,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 776,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 772,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => navigate(`/user/quiz/${id}/start`),\n            children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 15\n            }, this), \"Retake Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\",\n            onClick: () => navigate('/user/quiz'),\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"More Quizzes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"More\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 391,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"8UtYMs2kaaOfEWLjEoxheULc/i0=\", false, function () {\n  return [useParams, useNavigate, useLocation, useDispatch, useWindowSize];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useLocation", "useDispatch", "message", "Confetti", "useWindowSize", "TbCheck", "TbX", "TbTrophy", "TbBrain", "TbTarget", "TbRefresh", "TbBulb", "getExamById", "chatWithChatGPTToExplainAns", "getQuizResult", "HideLoading", "ShowLoading", "XPResultDisplay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "_location$state", "_result$correctAnswer", "_result$wrongAnswers", "examData", "setExamData", "questions", "setQuestions", "explanations", "setExplanations", "result", "setResult", "loading", "setLoading", "id", "navigate", "location", "dispatch", "width", "height", "resultFromState", "state", "console", "log", "xpData", "fetchData", "_examResponse$data", "examResponse", "examId", "success", "error", "data", "resultResponse", "_report$correctAnswer", "_report$correctAnswer2", "_report$wrongAnswers", "report", "transformedResult", "correctAnswers", "wrongAnswers", "verdict", "score", "percentage", "points", "length", "totalQuestions", "timeSpent", "totalTimeAllowed", "playSound", "createEnhancedSound", "frequencies", "durations", "volumes", "types", "stagger<PERSON><PERSON><PERSON>", "audioContext", "window", "AudioContext", "for<PERSON>ach", "frequency", "index", "oscillator", "createOscillator", "gainNode", "createGain", "delay", "connect", "destination", "setValueAtTime", "currentTime", "type", "volume", "duration", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "createChordSound", "createCelebrationSound", "createExcellentSound", "setTimeout", "createPassSound", "createFailSound", "document", "body", "classList", "add", "remove", "fetchExplanation", "question", "expectedAnswer", "userAnswer", "imageUrl", "response", "prev", "String", "explanation", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "getPerformanceLevel", "performanceLevel", "getPerformanceConfig", "bgGradient", "iconBg", "icon", "title", "subtitle", "confetti", "soundFile", "config", "style", "animation", "transform", "transform<PERSON><PERSON>in", "background", "filter", "color", "textShadow", "backgroundColor", "<PERSON><PERSON>ilter", "Math", "floor", "map", "_result$correctAnswer2", "_result$wrongAnswers$", "_id", "warn", "find", "q", "isCorrect", "some", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "border", "boxShadow", "fontWeight", "fontSize", "lineHeight", "name", "image", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport {\n  TbCheck,\n  TbX,\n  TbTrophy,\n  TbBrain,\n  TbTarget,\n  TbRefresh,\n  TbBulb\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { getQuizResult } from '../../../apicalls/quiz';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\n\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\n\nconst QuizResult = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [result, setResult] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const { width, height } = useWindowSize();\n\n  const resultFromState = location.state?.result;\n\n  // Debug result data\n  console.log('🎯 Quiz Result Page - Result data:', result);\n  console.log('💰 XP Data in result:', result?.xpData);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        dispatch(ShowLoading());\n\n        // Fetch exam data\n        const examResponse = await getExamById({ examId: id });\n\n        if (!examResponse.success) {\n          message.error(examResponse.message);\n          navigate('/user/quiz');\n          return;\n        }\n\n        setExamData(examResponse.data);\n        setQuestions(examResponse.data?.questions || []);\n\n        // If result is provided via state, use it\n        if (resultFromState) {\n          setResult(resultFromState);\n        } else {\n          // Otherwise, fetch the latest result for this quiz\n          const resultResponse = await getQuizResult(id);\n\n          if (resultResponse.success) {\n            // Transform the report data to match expected result format\n            const report = resultResponse.data;\n            const transformedResult = {\n              correctAnswers: report.correctAnswers || [],\n              wrongAnswers: report.wrongAnswers || [],\n              verdict: report.verdict,\n              score: report.percentage,\n              points: report.points || (report.correctAnswers?.length * 10) || 0,\n              totalQuestions: (report.correctAnswers?.length || 0) + (report.wrongAnswers?.length || 0),\n              timeSpent: report.timeSpent || 0,\n              totalTimeAllowed: report.totalTimeAllowed || 0,\n              xpData: report.xpData\n            };\n            setResult(transformedResult);\n          } else {\n            message.error('No quiz result found. Please take the quiz first.');\n            navigate('/user/quiz');\n            return;\n          }\n        }\n\n      } catch (error) {\n        message.error(error.message);\n        navigate('/user/quiz');\n      } finally {\n        setLoading(false);\n        dispatch(HideLoading());\n      }\n    };\n\n    if (id) {\n      fetchData();\n    }\n  }, [id, dispatch, navigate, resultFromState]);\n\n  // Play sound effect based on performance\n  useEffect(() => {\n    if (result) {\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n\n      // Play performance-based sound\n      const playSound = () => {\n        try {\n          const score = result.score || 0;\n\n          // Create enhanced sound effects using Web Audio API\n          const createEnhancedSound = (frequencies, durations, volumes = [0.3], types = ['sine'], staggerDelay = 0.15) => {\n            try {\n              const audioContext = new window.AudioContext();\n\n              frequencies.forEach((frequency, index) => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n                const delay = index * staggerDelay; // Customizable stagger timing\n\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + delay);\n                oscillator.type = types[index] || types[0] || 'sine';\n\n                const volume = volumes[index] || volumes[0] || 0.3;\n                const duration = durations[index] || durations[0] || 0.5;\n\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + delay + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + delay + duration);\n\n                oscillator.start(audioContext.currentTime + delay);\n                oscillator.stop(audioContext.currentTime + delay + duration);\n              });\n\n              return true;\n            } catch (error) {\n              console.log('Enhanced Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create chord sound (simultaneous notes) for richer harmonies\n          const createChordSound = (frequencies, duration, volume, type = 'sine') => {\n            try {\n              const audioContext = new window.AudioContext();\n\n              frequencies.forEach((frequency) => {\n                const oscillator = audioContext.createOscillator();\n                const gainNode = audioContext.createGain();\n\n                oscillator.connect(gainNode);\n                gainNode.connect(audioContext.destination);\n\n                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n                oscillator.type = type;\n\n                gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + 0.02);\n                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n\n                oscillator.start(audioContext.currentTime);\n                oscillator.stop(audioContext.currentTime + duration);\n              });\n\n              return true;\n            } catch (error) {\n              console.log('Chord Audio failed:', error);\n              return false;\n            }\n          };\n\n          // Create triumphant celebration sound for perfect scores\n          const createCelebrationSound = () => {\n            const frequencies = [523, 659, 784, 1047, 1319]; // C5, E5, G5, C6, E6 - Major chord progression\n            const durations = [0.25, 0.25, 0.25, 0.4, 0.6];\n            const volumes = [0.45, 0.45, 0.45, 0.5, 0.55];\n            const types = ['sine', 'triangle', 'sine', 'triangle', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create excellent achievement sound - bright chord followed by ascending notes\n          const createExcellentSound = () => {\n            // Play a bright major chord first\n            createChordSound([440, 554, 659], 0.4, 0.3, 'triangle'); // A4, C#5, E5\n            // Follow with ascending melody\n            setTimeout(() => {\n              const frequencies = [659, 784]; // E5, G5\n              const durations = [0.3, 0.4];\n              const volumes = [0.35, 0.4];\n              const types = ['sine', 'sine'];\n              createEnhancedSound(frequencies, durations, volumes, types, 0.2);\n            }, 300);\n            return true;\n          };\n\n          // Create encouraging pass sound - warm and positive\n          const createPassSound = () => {\n            const frequencies = [349, 440]; // F4, A4 - Simple positive interval\n            const durations = [0.4, 0.5];\n            const volumes = [0.35, 0.4];\n            const types = ['sine', 'triangle'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Create gentle encouragement sound for fails - supportive, not harsh\n          const createFailSound = () => {\n            const frequencies = [262, 294]; // C4, D4 - Gentle, hopeful interval\n            const durations = [0.5, 0.7];\n            const volumes = [0.3, 0.25];\n            const types = ['sine', 'sine'];\n            return createEnhancedSound(frequencies, durations, volumes, types);\n          };\n\n          // Play distinct sounds based on performance with unique characteristics\n          if (score === 100) {\n            // Perfect score - triumphant celebration with 5-note ascending sequence\n            createCelebrationSound();\n            // Add extra celebratory beeps\n            setTimeout(() => {\n              const frequencies = [1047, 1319, 1568]; // C6, E6, G6\n              const durations = [0.2, 0.2, 0.4];\n              const volumes = [0.3, 0.3, 0.35];\n              const types = ['triangle', 'triangle', 'triangle'];\n              createEnhancedSound(frequencies, durations, volumes, types);\n            }, 800);\n            console.log('🏆 PERFECT SCORE! 🎉');\n          } else if (score >= 80) {\n            // Excellent - bright 3-note major chord\n            createExcellentSound();\n            console.log('🎉 EXCELLENT! ⭐');\n          } else if (result.verdict === \"Pass\") {\n            // Pass - warm 2-note positive interval\n            createPassSound();\n            console.log('✅ Well Done! 🚀');\n          } else {\n            // Fail - gentle 2-note supportive tone (different from pass)\n            createFailSound();\n            // Add a gentle follow-up note for encouragement\n            setTimeout(() => {\n              const frequencies = [330]; // E4 - hopeful note\n              const durations = [0.6];\n              const volumes = [0.25];\n              const types = ['sine'];\n              createEnhancedSound(frequencies, durations, volumes, types);\n            }, 600);\n            console.log('💪 Keep Trying! 🌱');\n          }\n\n        } catch (error) {\n          console.log('Audio not supported:', error);\n          // Visual feedback as fallback\n          if (result.verdict === \"Pass\") {\n            console.log('🎉 Quiz Passed!');\n          } else {\n            console.log('💪 Keep trying!');\n          }\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playSound, 500);\n    }\n  }, [result]);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        setExplanations((prev) => ({ ...prev, [String(question)]: response.explanation }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  // Handle loading state\n  if (loading) {\n    return (\n      <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <h2 className=\"text-xl font-semibold text-gray-700 mb-2\">Loading Results</h2>\n          <p className=\"text-gray-500\">Please wait while we fetch your quiz results...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Handle missing result data\n  if (!result) {\n    return (\n      <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <TbTarget className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-semibold text-gray-700 mb-2\">No Result Data</h2>\n          <p className=\"text-gray-500 mb-4\">Unable to load quiz results.</p>\n          <button\n            onClick={() => navigate('/user/quiz')}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Quizzes\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Calculate performance level for animations and sounds\n  const getPerformanceLevel = () => {\n    const score = result.score || 0;\n    if (score === 100) return 'perfect';\n    if (score >= 80) return 'excellent';\n    if (score >= 60) return 'good';\n    if (result.verdict === \"Pass\") return 'pass';\n    return 'fail';\n  };\n\n  const performanceLevel = getPerformanceLevel();\n\n  // Performance-based styling and content\n  const getPerformanceConfig = () => {\n    switch (performanceLevel) {\n      case 'perfect':\n        return {\n          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',\n          iconBg: 'from-yellow-400 to-orange-500',\n          icon: TbTrophy,\n          title: '🏆 PERFECT SCORE!',\n          subtitle: 'Outstanding! You\\'re a quiz master! 🌟',\n          confetti: true,\n          soundFile: '/sounds/perfect.mp3'\n        };\n      case 'excellent':\n        return {\n          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',\n          iconBg: 'from-green-400 to-emerald-500',\n          icon: TbTrophy,\n          title: '🎉 EXCELLENT!',\n          subtitle: 'Amazing work! You\\'re doing great! ✨',\n          confetti: true,\n          soundFile: '/sounds/excellent.mp3'\n        };\n      case 'good':\n      case 'pass':\n        return {\n          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',\n          iconBg: 'from-blue-400 to-indigo-500',\n          icon: TbCheck,\n          title: '✅ Well Done!',\n          subtitle: 'Good job! Keep up the great work! 🚀',\n          confetti: result.verdict === \"Pass\",\n          soundFile: '/sounds/pass.mp3'\n        };\n      default:\n        return {\n          bgGradient: 'from-red-400 via-pink-500 to-rose-600',\n          iconBg: 'from-red-400 to-pink-500',\n          icon: TbX,\n          title: '💪 Keep Trying!',\n          subtitle: 'Don\\'t give up! Practice makes perfect! 🌱',\n          confetti: false,\n          soundFile: '/sounds/fail.mp3'\n        };\n    }\n  };\n\n  const config = getPerformanceConfig();\n\n\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden\">\n      {config.confetti && <Confetti width={width} height={height} />}\n\n      {/* Main Content - Scrollable */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <div className=\"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12\">\n\n          {/* Hero Section with Performance Animation */}\n          <div className={`bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`}>\n            {/* Animated Background Elements */}\n            <div className=\"absolute inset-0 overflow-hidden\">\n              <div className=\"absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse\"></div>\n              <div className=\"absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000\"></div>\n            </div>\n\n            <div className=\"relative z-10\">\n              {/* Animated Icon with Enhanced Effects */}\n              <div className={`inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 lg:w-40 lg:h-40 bg-gradient-to-br ${config.iconBg} rounded-full mb-6 sm:mb-8 shadow-2xl relative overflow-hidden`}\n                   style={{\n                     animation: performanceLevel === 'perfect' ? 'megaBounce 1.2s infinite, rainbowGlow 3s infinite, rotate360 4s infinite linear' :\n                               performanceLevel === 'excellent' ? 'bigBounce 1s infinite, excellentGlow 2.5s infinite' :\n                               performanceLevel === 'pass' || performanceLevel === 'good' ? 'gentlePulse 2s infinite, passGlow 3s infinite' :\n                               'encourageShake 0.8s ease-in-out 3, failGlow 2s infinite',\n                     transform: 'scale(1)',\n                     transformOrigin: 'center'\n                   }}>\n\n                {/* Sparkle Effects for Perfect Score */}\n                {performanceLevel === 'perfect' && (\n                  <>\n                    <div className=\"absolute inset-0 rounded-full\" style={{\n                      background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',\n                      animation: 'sparkle 1.5s infinite'\n                    }}></div>\n                    <div className=\"absolute top-2 right-2 w-3 h-3 bg-white rounded-full\" style={{\n                      animation: 'twinkle 1s infinite alternate'\n                    }}></div>\n                    <div className=\"absolute bottom-3 left-3 w-2 h-2 bg-yellow-300 rounded-full\" style={{\n                      animation: 'twinkle 1.2s infinite alternate-reverse'\n                    }}></div>\n                  </>\n                )}\n\n                <config.icon className=\"w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 text-white relative z-10\"\n                            style={{\n                              filter: performanceLevel === 'perfect' ? 'drop-shadow(0 0 10px rgba(255,255,255,0.8))' : 'none'\n                            }} />\n              </div>\n\n              {/* Title with Enhanced Animation */}\n              <h1 className=\"text-3xl sm:text-4xl lg:text-6xl font-black mb-4 sm:mb-6 relative\"\n                  style={{\n                    animation: performanceLevel === 'perfect' ? 'titleBounce 1.5s infinite, rainbowText 4s infinite' :\n                              performanceLevel === 'excellent' ? 'titlePulse 2s infinite, excellentText 3s infinite' :\n                              performanceLevel === 'pass' || performanceLevel === 'good' ? 'titleFadeIn 1s ease-out, passText 3s infinite' :\n                              'titleShake 0.6s ease-in-out 2, failText 2s infinite',\n                    color: 'white',\n                    textShadow: performanceLevel === 'perfect' ? '3px 3px 6px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.6)' :\n                               performanceLevel === 'excellent' ? '2px 2px 4px rgba(0,0,0,0.8), 0 0 15px rgba(255,255,255,0.4)' :\n                               '2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5)',\n                    transformOrigin: 'center'\n                  }}>\n                {config.title}\n\n                {/* Floating particles for perfect score */}\n                {performanceLevel === 'perfect' && (\n                  <div className=\"absolute inset-0 pointer-events-none\">\n                    <div className=\"absolute top-0 left-1/4 w-1 h-1 bg-yellow-300 rounded-full\" style={{\n                      animation: 'float 3s infinite ease-in-out'\n                    }}></div>\n                    <div className=\"absolute top-1/4 right-1/4 w-1 h-1 bg-white rounded-full\" style={{\n                      animation: 'float 2.5s infinite ease-in-out 0.5s'\n                    }}></div>\n                    <div className=\"absolute bottom-1/4 left-1/3 w-1 h-1 bg-yellow-400 rounded-full\" style={{\n                      animation: 'float 3.5s infinite ease-in-out 1s'\n                    }}></div>\n                  </div>\n                )}\n              </h1>\n\n              {/* Subtitle */}\n              <p className=\"text-base sm:text-lg lg:text-xl font-medium px-4 py-2 rounded-lg\"\n                 style={{\n                   color: 'white',\n                   textShadow: '1px 1px 3px rgba(0,0,0,0.8)',\n                   backgroundColor: 'rgba(0,0,0,0.3)',\n                   backdropFilter: 'blur(10px)'\n                 }}>\n                {config.subtitle}\n              </p>\n            </div>\n          </div>\n\n          {/* Stats Cards */}\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12\">\n            {/* Score Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2\">\n                {result.score || 0}%\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Score\n              </div>\n            </div>\n\n            {/* Correct Answers Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2\">\n                {result.correctAnswers?.length || 0}\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Correct\n              </div>\n            </div>\n\n            {/* Wrong Answers Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2\">\n                {result.wrongAnswers?.length || 0}\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Wrong\n              </div>\n            </div>\n\n            {/* Time Card */}\n            <div className=\"bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105\">\n              <div className=\"text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2\">\n                {Math.floor((result.timeSpent || 0) / 60)}m\n              </div>\n              <div className=\"text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide\">\n                Time\n              </div>\n            </div>\n          </div>\n\n          {/* XP Display */}\n          {result.xpData ? (\n            <div className=\"mb-6 sm:mb-8 lg:mb-12\">\n              <XPResultDisplay xpData={result.xpData} />\n            </div>\n          ) : (\n            /* Fallback XP Display */\n            <div className=\"mb-6 sm:mb-8 lg:mb-12\">\n              <div className=\"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-2xl p-6 border-2 border-yellow-200 shadow-lg text-center\">\n                <div className=\"flex items-center justify-center mb-3\">\n                  <TbBulb className=\"w-8 h-8 text-yellow-600 mr-2\" />\n                  <h3 className=\"text-2xl font-bold text-gray-800\">XP Earned</h3>\n                </div>\n                <div className=\"text-4xl font-bold text-yellow-600 mb-2\">\n                  +{result.points || (result.score * 10) || 100}\n                </div>\n                <p className=\"text-gray-600\">\n                  Great job completing this quiz!\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Enhanced Questions Summary for Learning */}\n          <div className=\"mb-6 sm:mb-8 lg:mb-12\">\n            <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8\">\n              <div className=\"text-center mb-6 sm:mb-8\">\n                <h3 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2\">\n                  📚 Learning Summary\n                </h3>\n                <p className=\"text-gray-600 text-sm sm:text-base\">\n                  Review your answers and learn from explanations to improve your understanding\n                </p>\n              </div>\n\n              <div className=\"space-y-6 sm:space-y-8\">\n                {questions.map((question, index) => {\n                  // Safety check to prevent rendering objects\n                  if (!question || typeof question !== 'object' || !question._id) {\n                    console.warn('Invalid question object at index:', index, question);\n                    return null;\n                  }\n\n                  const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||\n                                    result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || \"\";\n                  const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n                  const correctAnswer = question.correctAnswer || question.correctOption || 'N/A';\n\n                  return (\n                    <div key={index} className={`rounded-xl sm:rounded-2xl p-5 sm:p-6 lg:p-8 border-2 transition-all duration-300 ${\n                      isCorrect\n                        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 border-green-400 shadow-green-200'\n                        : 'bg-gradient-to-br from-red-50 via-pink-50 to-red-100 border-red-400 shadow-red-200'\n                    } shadow-lg hover:shadow-xl hover:scale-[1.02]`}>\n\n                      {/* Enhanced Question Header */}\n                      <div className=\"flex items-center justify-between mb-4 sm:mb-6\">\n                        <div className=\"flex items-center gap-3 sm:gap-4\">\n                          <div className={`flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-full font-bold text-white text-base sm:text-lg shadow-lg ${\n                            isCorrect ? 'bg-gradient-to-br from-green-500 to-emerald-600' : 'bg-gradient-to-br from-red-500 to-pink-600'\n                          }`}>\n                            {index + 1}\n                          </div>\n                          <div>\n                            <h4 className=\"text-lg sm:text-xl font-bold text-gray-900\">\n                              Question {index + 1}\n                            </h4>\n                          </div>\n                        </div>\n\n                        <div className={`flex items-center gap-2 px-4 sm:px-5 py-2 sm:py-3 rounded-full font-bold text-sm sm:text-base shadow-md ${\n                          isCorrect\n                            ? 'bg-green-500 text-white'\n                            : 'bg-red-500 text-white'\n                        }`}>\n                          {isCorrect ? (\n                            <>\n                              <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                              <span>Correct</span>\n                            </>\n                          ) : (\n                            <>\n                              <TbX className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                              <span>Incorrect</span>\n                            </>\n                          )}\n                        </div>\n                      </div>\n\n                      {/* Question Display */}\n                      <div className=\"mb-5 sm:mb-6\">\n                        <div className={`p-5 sm:p-6 rounded-xl border-3 shadow-md ${\n                          isCorrect\n                            ? 'bg-white border-green-500'\n                            : 'bg-white border-red-500'\n                        }`} style={{\n                          backgroundColor: '#ffffff',\n                          border: isCorrect ? '3px solid #22c55e' : '3px solid #ef4444',\n                          boxShadow: isCorrect ? '0 4px 15px rgba(34, 197, 94, 0.2)' : '0 4px 15px rgba(239, 68, 68, 0.2)'\n                        }}>\n                          <div className=\"text-gray-900 text-base sm:text-lg leading-relaxed font-bold\" style={{\n                            color: '#111827',\n                            fontWeight: '700',\n                            fontSize: '1.1rem',\n                            lineHeight: '1.7'\n                          }}>\n                            {String(question.name || 'Question')}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Question Image */}\n                      {question.image && (\n                        <div className=\"mb-5 sm:mb-6 text-center\">\n                          <div className=\"inline-block p-3 sm:p-4 bg-white rounded-lg border-2 border-gray-200 shadow-sm\">\n                            <img\n                              src={question.image}\n                              alt=\"Question Reference\"\n                              className=\"max-w-full max-h-40 sm:max-h-56 rounded-lg\"\n                            />\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Answer Analysis */}\n                      <div className=\"space-y-4 sm:space-y-5\">\n                        {/* Your Answer */}\n                        <div>\n                          <h5 className=\"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\">\n                            <span className=\"text-blue-600\">👤</span>\n                            Your Answer:\n                          </h5>\n                          <div className={`flex items-start gap-3 sm:gap-4 p-5 sm:p-6 rounded-xl font-bold text-base sm:text-lg border-3 shadow-md ${\n                            isCorrect\n                              ? 'bg-green-50 text-green-900 border-green-500'\n                              : 'bg-red-50 text-red-900 border-red-500'\n                          }`} style={{\n                            border: isCorrect ? '3px solid #22c55e' : '3px solid #ef4444',\n                            boxShadow: isCorrect ? '0 4px 15px rgba(34, 197, 94, 0.2)' : '0 4px 15px rgba(239, 68, 68, 0.2)'\n                          }}>\n                            <div className=\"flex-shrink-0 mt-1\">\n                              {isCorrect ? (\n                                <div className=\"flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 bg-green-500 rounded-full shadow-lg\">\n                                  <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6 text-white\" />\n                                </div>\n                              ) : (\n                                <div className=\"flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 bg-red-500 rounded-full shadow-lg\">\n                                  <TbX className=\"w-5 h-5 sm:w-6 sm:h-6 text-white\" />\n                                </div>\n                              )}\n                            </div>\n                            <div className=\"flex-1\">\n                              <div className=\"text-gray-900 font-bold text-lg\" style={{\n                                color: '#111827',\n                                fontWeight: '700',\n                                fontSize: '1.1rem'\n                              }}>\n                                {String(userAnswer || 'No answer provided')}\n                              </div>\n                              {isCorrect && (\n                                <p className=\"text-green-800 text-sm sm:text-base mt-2 font-bold\">\n                                  🎉 Excellent! This is the correct answer.\n                                </p>\n                              )}\n                              {!isCorrect && userAnswer && (\n                                <p className=\"text-red-800 text-sm sm:text-base mt-2 font-bold\">\n                                  ❌ This answer is incorrect. Let's learn why below.\n                                </p>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Correct Answer */}\n                        <div>\n                          <h5 className=\"font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2\">\n                            <span className=\"text-green-600\">✅</span>\n                            Correct Answer:\n                          </h5>\n                          <div className=\"flex items-start gap-3 sm:gap-4 p-5 sm:p-6 bg-green-50 text-green-900 rounded-xl font-bold text-base sm:text-lg border-3 border-green-500 shadow-md\" style={{\n                            border: '3px solid #22c55e',\n                            boxShadow: '0 4px 15px rgba(34, 197, 94, 0.2)'\n                          }}>\n                            <div className=\"flex-shrink-0 mt-1\">\n                              <div className=\"flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 bg-green-500 rounded-full shadow-lg\">\n                                <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6 text-white\" />\n                              </div>\n                            </div>\n                            <div className=\"flex-1\">\n                              <div className=\"text-gray-900 font-bold text-lg\" style={{\n                                color: '#111827',\n                                fontWeight: '700',\n                                fontSize: '1.1rem'\n                              }}>\n                                {String(correctAnswer || 'N/A')}\n                              </div>\n                              <p className=\"text-green-800 text-sm sm:text-base mt-2 font-bold\">\n                                💡 Remember this answer for future reference!\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* AI Explanation for Wrong Answers */}\n                        {!isCorrect && (\n                          <div className=\"mt-4 sm:mt-5\">\n                            <button\n                              onClick={() => fetchExplanation(\n                                String(question.name || 'Question'),\n                                String(correctAnswer || 'N/A'),\n                                String(userAnswer || ''),\n                                question.image\n                              )}\n                              className=\"w-full flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl\"\n                            >\n                              <TbBulb className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                              <span>Get Explanation</span>\n                            </button>\n\n                            {explanations[String(question.name || 'Question')] && (\n                              <div className=\"mt-4 p-5 sm:p-6 bg-blue-50 border-3 border-blue-400 rounded-xl shadow-md\" style={{\n                                border: '3px solid #3b82f6',\n                                boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2)'\n                              }}>\n                                <div className=\"flex items-start gap-3 mb-4\">\n                                  <div className=\"flex items-center justify-center w-10 h-10 bg-blue-500 rounded-full flex-shrink-0 shadow-lg\">\n                                    <TbBulb className=\"w-6 h-6 text-white\" />\n                                  </div>\n                                  <h6 className=\"font-bold text-blue-900 text-lg sm:text-xl\">Explanation:</h6>\n                                </div>\n                                <div className=\"text-gray-900 text-base sm:text-lg leading-relaxed font-medium\" style={{\n                                  color: '#111827',\n                                  fontWeight: '600',\n                                  lineHeight: '1.7'\n                                }}>\n                                  {String(explanations[String(question.name || 'Question')] || '')}\n                                </div>\n                              </div>\n                            )}\n                          </div>\n                        )}\n\n                        {/* Encouragement for Correct Answers */}\n                        {isCorrect && (\n                          <div className=\"mt-4 p-4 bg-green-50 border-2 border-green-200 rounded-lg\">\n                            <div className=\"flex items-center gap-3\">\n                              <div className=\"flex items-center justify-center w-8 h-8 bg-green-500 rounded-full\">\n                                <TbCheck className=\"w-5 h-5 text-white\" />\n                              </div>\n                              <div>\n                                <h6 className=\"font-bold text-green-900 text-sm sm:text-base\">Great job!</h6>\n                                <p className=\"text-green-700 text-xs sm:text-sm\">\n                                  You demonstrated good understanding of this concept. Keep it up! 🌟\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8\">\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => navigate(`/user/quiz/${id}/start`)}\n            >\n              <TbRefresh className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\" />\n              Retake Quiz\n            </button>\n\n            <button\n              className=\"group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation\"\n              onClick={() => navigate('/user/quiz')}\n            >\n              <TbBrain className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n              <span className=\"hidden sm:inline\">More Quizzes</span>\n              <span className=\"sm:hidden\">More</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SACEC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACTC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAErE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,oBAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM;IAAEyC;EAAG,CAAC,GAAGvC,SAAS,CAAC,CAAC;EAC1B,MAAMwC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAMwC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAMwC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwC,KAAK;IAAEC;EAAO,CAAC,GAAGtC,aAAa,CAAC,CAAC;EAEzC,MAAMuC,eAAe,IAAAnB,eAAA,GAAGe,QAAQ,CAACK,KAAK,cAAApB,eAAA,uBAAdA,eAAA,CAAgBS,MAAM;;EAE9C;EACAY,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEb,MAAM,CAAC;EACzDY,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEb,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEc,MAAM,CAAC;EAEpDlD,SAAS,CAAC,MAAM;IACd,MAAMmD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QAAA,IAAAC,kBAAA;QACFb,UAAU,CAAC,IAAI,CAAC;QAChBI,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;;QAEvB;QACA,MAAMkC,YAAY,GAAG,MAAMtC,WAAW,CAAC;UAAEuC,MAAM,EAAEd;QAAG,CAAC,CAAC;QAEtD,IAAI,CAACa,YAAY,CAACE,OAAO,EAAE;UACzBlD,OAAO,CAACmD,KAAK,CAACH,YAAY,CAAChD,OAAO,CAAC;UACnCoC,QAAQ,CAAC,YAAY,CAAC;UACtB;QACF;QAEAV,WAAW,CAACsB,YAAY,CAACI,IAAI,CAAC;QAC9BxB,YAAY,CAAC,EAAAmB,kBAAA,GAAAC,YAAY,CAACI,IAAI,cAAAL,kBAAA,uBAAjBA,kBAAA,CAAmBpB,SAAS,KAAI,EAAE,CAAC;;QAEhD;QACA,IAAIc,eAAe,EAAE;UACnBT,SAAS,CAACS,eAAe,CAAC;QAC5B,CAAC,MAAM;UACL;UACA,MAAMY,cAAc,GAAG,MAAMzC,aAAa,CAACuB,EAAE,CAAC;UAE9C,IAAIkB,cAAc,CAACH,OAAO,EAAE;YAAA,IAAAI,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA;YAC1B;YACA,MAAMC,MAAM,GAAGJ,cAAc,CAACD,IAAI;YAClC,MAAMM,iBAAiB,GAAG;cACxBC,cAAc,EAAEF,MAAM,CAACE,cAAc,IAAI,EAAE;cAC3CC,YAAY,EAAEH,MAAM,CAACG,YAAY,IAAI,EAAE;cACvCC,OAAO,EAAEJ,MAAM,CAACI,OAAO;cACvBC,KAAK,EAAEL,MAAM,CAACM,UAAU;cACxBC,MAAM,EAAEP,MAAM,CAACO,MAAM,IAAK,EAAAV,qBAAA,GAAAG,MAAM,CAACE,cAAc,cAAAL,qBAAA,uBAArBA,qBAAA,CAAuBW,MAAM,IAAG,EAAG,IAAI,CAAC;cAClEC,cAAc,EAAE,CAAC,EAAAX,sBAAA,GAAAE,MAAM,CAACE,cAAc,cAAAJ,sBAAA,uBAArBA,sBAAA,CAAuBU,MAAM,KAAI,CAAC,KAAK,EAAAT,oBAAA,GAAAC,MAAM,CAACG,YAAY,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAqBS,MAAM,KAAI,CAAC,CAAC;cACzFE,SAAS,EAAEV,MAAM,CAACU,SAAS,IAAI,CAAC;cAChCC,gBAAgB,EAAEX,MAAM,CAACW,gBAAgB,IAAI,CAAC;cAC9CvB,MAAM,EAAEY,MAAM,CAACZ;YACjB,CAAC;YACDb,SAAS,CAAC0B,iBAAiB,CAAC;UAC9B,CAAC,MAAM;YACL1D,OAAO,CAACmD,KAAK,CAAC,mDAAmD,CAAC;YAClEf,QAAQ,CAAC,YAAY,CAAC;YACtB;UACF;QACF;MAEF,CAAC,CAAC,OAAOe,KAAK,EAAE;QACdnD,OAAO,CAACmD,KAAK,CAACA,KAAK,CAACnD,OAAO,CAAC;QAC5BoC,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;QACjBI,QAAQ,CAACzB,WAAW,CAAC,CAAC,CAAC;MACzB;IACF,CAAC;IAED,IAAIsB,EAAE,EAAE;MACNW,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACX,EAAE,EAAEG,QAAQ,EAAEF,QAAQ,EAAEK,eAAe,CAAC,CAAC;;EAE7C;EACA9C,SAAS,CAAC,MAAM;IACd,IAAIoC,MAAM,EAAE;MACVY,OAAO,CAACC,GAAG,CAAE,QAAOb,MAAM,CAAC8B,OAAO,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAS,GAAE,CAAC;;MAEvE;MACA,MAAMQ,SAAS,GAAGA,CAAA,KAAM;QACtB,IAAI;UACF,MAAMP,KAAK,GAAG/B,MAAM,CAAC+B,KAAK,IAAI,CAAC;;UAE/B;UACA,MAAMQ,mBAAmB,GAAGA,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAEC,YAAY,GAAG,IAAI,KAAK;YAC9G,IAAI;cACF,MAAMC,YAAY,GAAG,IAAIC,MAAM,CAACC,YAAY,CAAC,CAAC;cAE9CP,WAAW,CAACQ,OAAO,CAAC,CAACC,SAAS,EAAEC,KAAK,KAAK;gBACxC,MAAMC,UAAU,GAAGN,YAAY,CAACO,gBAAgB,CAAC,CAAC;gBAClD,MAAMC,QAAQ,GAAGR,YAAY,CAACS,UAAU,CAAC,CAAC;gBAC1C,MAAMC,KAAK,GAAGL,KAAK,GAAGN,YAAY,CAAC,CAAC;;gBAEpCO,UAAU,CAACK,OAAO,CAACH,QAAQ,CAAC;gBAC5BA,QAAQ,CAACG,OAAO,CAACX,YAAY,CAACY,WAAW,CAAC;gBAE1CN,UAAU,CAACF,SAAS,CAACS,cAAc,CAACT,SAAS,EAAEJ,YAAY,CAACc,WAAW,GAAGJ,KAAK,CAAC;gBAChFJ,UAAU,CAACS,IAAI,GAAGjB,KAAK,CAACO,KAAK,CAAC,IAAIP,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM;gBAEpD,MAAMkB,MAAM,GAAGnB,OAAO,CAACQ,KAAK,CAAC,IAAIR,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG;gBAClD,MAAMoB,QAAQ,GAAGrB,SAAS,CAACS,KAAK,CAAC,IAAIT,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG;gBAExDY,QAAQ,CAACU,IAAI,CAACL,cAAc,CAAC,CAAC,EAAEb,YAAY,CAACc,WAAW,GAAGJ,KAAK,CAAC;gBACjEF,QAAQ,CAACU,IAAI,CAACC,uBAAuB,CAACH,MAAM,EAAEhB,YAAY,CAACc,WAAW,GAAGJ,KAAK,GAAG,IAAI,CAAC;gBACtFF,QAAQ,CAACU,IAAI,CAACE,4BAA4B,CAAC,KAAK,EAAEpB,YAAY,CAACc,WAAW,GAAGJ,KAAK,GAAGO,QAAQ,CAAC;gBAE9FX,UAAU,CAACe,KAAK,CAACrB,YAAY,CAACc,WAAW,GAAGJ,KAAK,CAAC;gBAClDJ,UAAU,CAACgB,IAAI,CAACtB,YAAY,CAACc,WAAW,GAAGJ,KAAK,GAAGO,QAAQ,CAAC;cAC9D,CAAC,CAAC;cAEF,OAAO,IAAI;YACb,CAAC,CAAC,OAAO1C,KAAK,EAAE;cACdR,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,KAAK,CAAC;cAC5C,OAAO,KAAK;YACd;UACF,CAAC;;UAED;UACA,MAAMgD,gBAAgB,GAAGA,CAAC5B,WAAW,EAAEsB,QAAQ,EAAED,MAAM,EAAED,IAAI,GAAG,MAAM,KAAK;YACzE,IAAI;cACF,MAAMf,YAAY,GAAG,IAAIC,MAAM,CAACC,YAAY,CAAC,CAAC;cAE9CP,WAAW,CAACQ,OAAO,CAAEC,SAAS,IAAK;gBACjC,MAAME,UAAU,GAAGN,YAAY,CAACO,gBAAgB,CAAC,CAAC;gBAClD,MAAMC,QAAQ,GAAGR,YAAY,CAACS,UAAU,CAAC,CAAC;gBAE1CH,UAAU,CAACK,OAAO,CAACH,QAAQ,CAAC;gBAC5BA,QAAQ,CAACG,OAAO,CAACX,YAAY,CAACY,WAAW,CAAC;gBAE1CN,UAAU,CAACF,SAAS,CAACS,cAAc,CAACT,SAAS,EAAEJ,YAAY,CAACc,WAAW,CAAC;gBACxER,UAAU,CAACS,IAAI,GAAGA,IAAI;gBAEtBP,QAAQ,CAACU,IAAI,CAACL,cAAc,CAAC,CAAC,EAAEb,YAAY,CAACc,WAAW,CAAC;gBACzDN,QAAQ,CAACU,IAAI,CAACC,uBAAuB,CAACH,MAAM,EAAEhB,YAAY,CAACc,WAAW,GAAG,IAAI,CAAC;gBAC9EN,QAAQ,CAACU,IAAI,CAACE,4BAA4B,CAAC,KAAK,EAAEpB,YAAY,CAACc,WAAW,GAAGG,QAAQ,CAAC;gBAEtFX,UAAU,CAACe,KAAK,CAACrB,YAAY,CAACc,WAAW,CAAC;gBAC1CR,UAAU,CAACgB,IAAI,CAACtB,YAAY,CAACc,WAAW,GAAGG,QAAQ,CAAC;cACtD,CAAC,CAAC;cAEF,OAAO,IAAI;YACb,CAAC,CAAC,OAAO1C,KAAK,EAAE;cACdR,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,KAAK,CAAC;cACzC,OAAO,KAAK;YACd;UACF,CAAC;;UAED;UACA,MAAMiD,sBAAsB,GAAGA,CAAA,KAAM;YACnC,MAAM7B,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;YACjD,MAAMC,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;YAC9C,MAAMC,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;YAC7C,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC;YAC9D,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,MAAM2B,oBAAoB,GAAGA,CAAA,KAAM;YACjC;YACAF,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC;YACzD;YACAG,UAAU,CAAC,MAAM;cACf,MAAM/B,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;cAChC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;cAC5B,MAAMC,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;cAC3B,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;cAC9BJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAE,GAAG,CAAC;YAClE,CAAC,EAAE,GAAG,CAAC;YACP,OAAO,IAAI;UACb,CAAC;;UAED;UACA,MAAM6B,eAAe,GAAGA,CAAA,KAAM;YAC5B,MAAMhC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAChC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;YAC5B,MAAMC,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;YAC3B,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;YAClC,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,MAAM8B,eAAe,GAAGA,CAAA,KAAM;YAC5B,MAAMjC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAChC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;YAC5B,MAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;YAC3B,MAAMC,KAAK,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;YAC9B,OAAOJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;UACpE,CAAC;;UAED;UACA,IAAIZ,KAAK,KAAK,GAAG,EAAE;YACjB;YACAsC,sBAAsB,CAAC,CAAC;YACxB;YACAE,UAAU,CAAC,MAAM;cACf,MAAM/B,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;cACxC,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;cACjC,MAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;cAChC,MAAMC,KAAK,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;cAClDJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;YAC7D,CAAC,EAAE,GAAG,CAAC;YACP/B,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;UACrC,CAAC,MAAM,IAAIkB,KAAK,IAAI,EAAE,EAAE;YACtB;YACAuC,oBAAoB,CAAC,CAAC;YACtB1D,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,MAAM,IAAIb,MAAM,CAAC8B,OAAO,KAAK,MAAM,EAAE;YACpC;YACA0C,eAAe,CAAC,CAAC;YACjB5D,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,MAAM;YACL;YACA4D,eAAe,CAAC,CAAC;YACjB;YACAF,UAAU,CAAC,MAAM;cACf,MAAM/B,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;cAC3B,MAAMC,SAAS,GAAG,CAAC,GAAG,CAAC;cACvB,MAAMC,OAAO,GAAG,CAAC,IAAI,CAAC;cACtB,MAAMC,KAAK,GAAG,CAAC,MAAM,CAAC;cACtBJ,mBAAmB,CAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;YAC7D,CAAC,EAAE,GAAG,CAAC;YACP/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACnC;QAEF,CAAC,CAAC,OAAOO,KAAK,EAAE;UACdR,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,KAAK,CAAC;UAC1C;UACA,IAAIpB,MAAM,CAAC8B,OAAO,KAAK,MAAM,EAAE;YAC7BlB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,MAAM;YACLD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAChC;QACF;MACF,CAAC;;MAED;MACA0D,UAAU,CAACjC,SAAS,EAAE,GAAG,CAAC;IAC5B;EACF,CAAC,EAAE,CAACtC,MAAM,CAAC,CAAC;EAEZpC,SAAS,CAAC,MAAM;IACd8G,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IACjF,IAAI;MACF5E,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMqG,QAAQ,GAAG,MAAMxG,2BAA2B,CAAC;QAAEoG,QAAQ;QAAEC,cAAc;QAAEC,UAAU;QAAEC;MAAS,CAAC,CAAC;MACtG5E,QAAQ,CAACzB,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIsG,QAAQ,CAACjE,OAAO,EAAE;QACpBpB,eAAe,CAAEsF,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAACC,MAAM,CAACN,QAAQ,CAAC,GAAGI,QAAQ,CAACG;QAAY,CAAC,CAAC,CAAC;MACpF,CAAC,MAAM;QACLtH,OAAO,CAACmD,KAAK,CAACgE,QAAQ,CAAChE,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdb,QAAQ,CAACzB,WAAW,CAAC,CAAC,CAAC;MACvBb,OAAO,CAACmD,KAAK,CAACA,KAAK,CAACnD,OAAO,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,IAAIiC,OAAO,EAAE;IACX,oBACEhB,OAAA;MAAKsG,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACrGvG,OAAA;QAAKsG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvG,OAAA;UAAKsG,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG3G,OAAA;UAAIsG,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E3G,OAAA;UAAGsG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAAC7F,MAAM,EAAE;IACX,oBACEd,OAAA;MAAKsG,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACrGvG,OAAA;QAAKsG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvG,OAAA,CAACV,QAAQ;UAACgH,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D3G,OAAA;UAAIsG,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E3G,OAAA;UAAGsG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClE3G,OAAA;UACE4G,OAAO,EAAEA,CAAA,KAAMzF,QAAQ,CAAC,YAAY,CAAE;UACtCmF,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMhE,KAAK,GAAG/B,MAAM,CAAC+B,KAAK,IAAI,CAAC;IAC/B,IAAIA,KAAK,KAAK,GAAG,EAAE,OAAO,SAAS;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,WAAW;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,MAAM;IAC9B,IAAI/B,MAAM,CAAC8B,OAAO,KAAK,MAAM,EAAE,OAAO,MAAM;IAC5C,OAAO,MAAM;EACf,CAAC;EAED,MAAMkE,gBAAgB,GAAGD,mBAAmB,CAAC,CAAC;;EAE9C;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQD,gBAAgB;MACtB,KAAK,SAAS;QACZ,OAAO;UACLE,UAAU,EAAE,2CAA2C;UACvDC,MAAM,EAAE,+BAA+B;UACvCC,IAAI,EAAE9H,QAAQ;UACd+H,KAAK,EAAE,mBAAmB;UAC1BC,QAAQ,EAAE,wCAAwC;UAClDC,QAAQ,EAAE,IAAI;UACdC,SAAS,EAAE;QACb,CAAC;MACH,KAAK,WAAW;QACd,OAAO;UACLN,UAAU,EAAE,4CAA4C;UACxDC,MAAM,EAAE,+BAA+B;UACvCC,IAAI,EAAE9H,QAAQ;UACd+H,KAAK,EAAE,eAAe;UACtBC,QAAQ,EAAE,sCAAsC;UAChDC,QAAQ,EAAE,IAAI;UACdC,SAAS,EAAE;QACb,CAAC;MACH,KAAK,MAAM;MACX,KAAK,MAAM;QACT,OAAO;UACLN,UAAU,EAAE,4CAA4C;UACxDC,MAAM,EAAE,6BAA6B;UACrCC,IAAI,EAAEhI,OAAO;UACbiI,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,sCAAsC;UAChDC,QAAQ,EAAEvG,MAAM,CAAC8B,OAAO,KAAK,MAAM;UACnC0E,SAAS,EAAE;QACb,CAAC;MACH;QACE,OAAO;UACLN,UAAU,EAAE,uCAAuC;UACnDC,MAAM,EAAE,0BAA0B;UAClCC,IAAI,EAAE/H,GAAG;UACTgI,KAAK,EAAE,iBAAiB;UACxBC,QAAQ,EAAE,4CAA4C;UACtDC,QAAQ,EAAE,KAAK;UACfC,SAAS,EAAE;QACb,CAAC;IACL;EACF,CAAC;EAED,MAAMC,MAAM,GAAGR,oBAAoB,CAAC,CAAC;EAIrC,oBACE/G,OAAA;IAAKsG,SAAS,EAAC,qFAAqF;IAAAC,QAAA,GACjGgB,MAAM,CAACF,QAAQ,iBAAIrH,OAAA,CAAChB,QAAQ;MAACsC,KAAK,EAAEA,KAAM;MAACC,MAAM,EAAEA;IAAO;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG9D3G,OAAA;MAAKsG,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCvG,OAAA;QAAKsG,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAG3EvG,OAAA;UAAKsG,SAAS,EAAG,qBAAoBiB,MAAM,CAACP,UAAW,sHAAsH;UAAAT,QAAA,gBAE3KvG,OAAA;YAAKsG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CvG,OAAA;cAAKsG,SAAS,EAAC;YAAsF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5G3G,OAAA;cAAKsG,SAAS,EAAC;YAAmG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC,eAEN3G,OAAA;YAAKsG,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAE5BvG,OAAA;cAAKsG,SAAS,EAAG,uGAAsGiB,MAAM,CAACN,MAAO,gEAAgE;cAChMO,KAAK,EAAE;gBACLC,SAAS,EAAEX,gBAAgB,KAAK,SAAS,GAAG,iFAAiF,GACnHA,gBAAgB,KAAK,WAAW,GAAG,oDAAoD,GACvFA,gBAAgB,KAAK,MAAM,IAAIA,gBAAgB,KAAK,MAAM,GAAG,+CAA+C,GAC5G,yDAAyD;gBACnEY,SAAS,EAAE,UAAU;gBACrBC,eAAe,EAAE;cACnB,CAAE;cAAApB,QAAA,GAGJO,gBAAgB,KAAK,SAAS,iBAC7B9G,OAAA,CAAAE,SAAA;gBAAAqG,QAAA,gBACEvG,OAAA;kBAAKsG,SAAS,EAAC,+BAA+B;kBAACkB,KAAK,EAAE;oBACpDI,UAAU,EAAE,oEAAoE;oBAChFH,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT3G,OAAA;kBAAKsG,SAAS,EAAC,sDAAsD;kBAACkB,KAAK,EAAE;oBAC3EC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT3G,OAAA;kBAAKsG,SAAS,EAAC,6DAA6D;kBAACkB,KAAK,EAAE;oBAClFC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACT,CACH,eAED3G,OAAA,CAACuH,MAAM,CAACL,IAAI;gBAACZ,SAAS,EAAC,oEAAoE;gBAC/EkB,KAAK,EAAE;kBACLK,MAAM,EAAEf,gBAAgB,KAAK,SAAS,GAAG,6CAA6C,GAAG;gBAC3F;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAGN3G,OAAA;cAAIsG,SAAS,EAAC,mEAAmE;cAC7EkB,KAAK,EAAE;gBACLC,SAAS,EAAEX,gBAAgB,KAAK,SAAS,GAAG,oDAAoD,GACtFA,gBAAgB,KAAK,WAAW,GAAG,mDAAmD,GACtFA,gBAAgB,KAAK,MAAM,IAAIA,gBAAgB,KAAK,MAAM,GAAG,+CAA+C,GAC5G,qDAAqD;gBAC/DgB,KAAK,EAAE,OAAO;gBACdC,UAAU,EAAEjB,gBAAgB,KAAK,SAAS,GAAG,6DAA6D,GAC/FA,gBAAgB,KAAK,WAAW,GAAG,6DAA6D,GAChG,uDAAuD;gBAClEa,eAAe,EAAE;cACnB,CAAE;cAAApB,QAAA,GACHgB,MAAM,CAACJ,KAAK,EAGZL,gBAAgB,KAAK,SAAS,iBAC7B9G,OAAA;gBAAKsG,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,gBACnDvG,OAAA;kBAAKsG,SAAS,EAAC,4DAA4D;kBAACkB,KAAK,EAAE;oBACjFC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT3G,OAAA;kBAAKsG,SAAS,EAAC,0DAA0D;kBAACkB,KAAK,EAAE;oBAC/EC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT3G,OAAA;kBAAKsG,SAAS,EAAC,iEAAiE;kBAACkB,KAAK,EAAE;oBACtFC,SAAS,EAAE;kBACb;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGL3G,OAAA;cAAGsG,SAAS,EAAC,kEAAkE;cAC5EkB,KAAK,EAAE;gBACLM,KAAK,EAAE,OAAO;gBACdC,UAAU,EAAE,6BAA6B;gBACzCC,eAAe,EAAE,iBAAiB;gBAClCC,cAAc,EAAE;cAClB,CAAE;cAAA1B,QAAA,EACFgB,MAAM,CAACH;YAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3G,OAAA;UAAKsG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBAEnFvG,OAAA;YAAKsG,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvG,OAAA;cAAKsG,SAAS,EAAC,gEAAgE;cAAAC,QAAA,GAC5EzF,MAAM,CAAC+B,KAAK,IAAI,CAAC,EAAC,GACrB;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3G,OAAA;YAAKsG,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvG,OAAA;cAAKsG,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAC7E,EAAAjG,qBAAA,GAAAQ,MAAM,CAAC4B,cAAc,cAAApC,qBAAA,uBAArBA,qBAAA,CAAuB0C,MAAM,KAAI;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3G,OAAA;YAAKsG,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvG,OAAA;cAAKsG,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC3E,EAAAhG,oBAAA,GAAAO,MAAM,CAAC6B,YAAY,cAAApC,oBAAA,uBAAnBA,oBAAA,CAAqByC,MAAM,KAAI;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN3G,OAAA;YAAKsG,SAAS,EAAC,2IAA2I;YAAAC,QAAA,gBACxJvG,OAAA;cAAKsG,SAAS,EAAC,kEAAkE;cAAAC,QAAA,GAC9E2B,IAAI,CAACC,KAAK,CAAC,CAACrH,MAAM,CAACoC,SAAS,IAAI,CAAC,IAAI,EAAE,CAAC,EAAC,GAC5C;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAExF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL7F,MAAM,CAACc,MAAM,gBACZ5B,OAAA;UAAKsG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCvG,OAAA,CAACF,eAAe;YAAC8B,MAAM,EAAEd,MAAM,CAACc;UAAO;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;QAAA;QAEN;QACA3G,OAAA;UAAKsG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCvG,OAAA;YAAKsG,SAAS,EAAC,gHAAgH;YAAAC,QAAA,gBAC7HvG,OAAA;cAAKsG,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDvG,OAAA,CAACR,MAAM;gBAAC8G,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnD3G,OAAA;gBAAIsG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACN3G,OAAA;cAAKsG,SAAS,EAAC,yCAAyC;cAAAC,QAAA,GAAC,GACtD,EAACzF,MAAM,CAACiC,MAAM,IAAKjC,MAAM,CAAC+B,KAAK,GAAG,EAAG,IAAI,GAAG;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN3G,OAAA;cAAGsG,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD3G,OAAA;UAAKsG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCvG,OAAA;YAAKsG,SAAS,EAAC,gEAAgE;YAAAC,QAAA,gBAC7EvG,OAAA;cAAKsG,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCvG,OAAA;gBAAIsG,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,EAAC;cAE7E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3G,OAAA;gBAAGsG,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN3G,OAAA;cAAKsG,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpC7F,SAAS,CAAC0H,GAAG,CAAC,CAACtC,QAAQ,EAAE9B,KAAK,KAAK;gBAAA,IAAAqE,sBAAA,EAAAC,qBAAA;gBAClC;gBACA,IAAI,CAACxC,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,CAACyC,GAAG,EAAE;kBAC9D7G,OAAO,CAAC8G,IAAI,CAAC,mCAAmC,EAAExE,KAAK,EAAE8B,QAAQ,CAAC;kBAClE,OAAO,IAAI;gBACb;gBAEA,MAAME,UAAU,GAAG,EAAAqC,sBAAA,GAAAvH,MAAM,CAAC4B,cAAc,CAAC+F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,GAAG,KAAKzC,QAAQ,CAACyC,GAAG,CAAC,cAAAF,sBAAA,uBAAvDA,sBAAA,CAAyDrC,UAAU,OAAAsC,qBAAA,GACpExH,MAAM,CAAC6B,YAAY,CAAC8F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,GAAG,KAAKzC,QAAQ,CAACyC,GAAG,CAAC,cAAAD,qBAAA,uBAArDA,qBAAA,CAAuDtC,UAAU,KAAI,EAAE;gBACzF,MAAM2C,SAAS,GAAG7H,MAAM,CAAC4B,cAAc,CAACkG,IAAI,CAACF,CAAC,IAAIA,CAAC,CAACH,GAAG,KAAKzC,QAAQ,CAACyC,GAAG,CAAC;gBACzE,MAAMM,aAAa,GAAG/C,QAAQ,CAAC+C,aAAa,IAAI/C,QAAQ,CAACgD,aAAa,IAAI,KAAK;gBAE/E,oBACE9I,OAAA;kBAAiBsG,SAAS,EAAG,oFAC3BqC,SAAS,GACL,+FAA+F,GAC/F,oFACL,+CAA+C;kBAAApC,QAAA,gBAG9CvG,OAAA;oBAAKsG,SAAS,EAAC,gDAAgD;oBAAAC,QAAA,gBAC7DvG,OAAA;sBAAKsG,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/CvG,OAAA;wBAAKsG,SAAS,EAAG,+HACfqC,SAAS,GAAG,iDAAiD,GAAG,4CACjE,EAAE;wBAAApC,QAAA,EACAvC,KAAK,GAAG;sBAAC;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC,eACN3G,OAAA;wBAAAuG,QAAA,eACEvG,OAAA;0BAAIsG,SAAS,EAAC,4CAA4C;0BAAAC,QAAA,GAAC,WAChD,EAACvC,KAAK,GAAG,CAAC;wBAAA;0BAAAwC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN3G,OAAA;sBAAKsG,SAAS,EAAG,2GACfqC,SAAS,GACL,yBAAyB,GACzB,uBACL,EAAE;sBAAApC,QAAA,EACAoC,SAAS,gBACR3I,OAAA,CAAAE,SAAA;wBAAAqG,QAAA,gBACEvG,OAAA,CAACd,OAAO;0BAACoH,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7C3G,OAAA;0BAAAuG,QAAA,EAAM;wBAAO;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,eACpB,CAAC,gBAEH3G,OAAA,CAAAE,SAAA;wBAAAqG,QAAA,gBACEvG,OAAA,CAACb,GAAG;0BAACmH,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACzC3G,OAAA;0BAAAuG,QAAA,EAAM;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,eACtB;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN3G,OAAA;oBAAKsG,SAAS,EAAC,cAAc;oBAAAC,QAAA,eAC3BvG,OAAA;sBAAKsG,SAAS,EAAG,4CACfqC,SAAS,GACL,2BAA2B,GAC3B,yBACL,EAAE;sBAACnB,KAAK,EAAE;wBACTQ,eAAe,EAAE,SAAS;wBAC1Be,MAAM,EAAEJ,SAAS,GAAG,mBAAmB,GAAG,mBAAmB;wBAC7DK,SAAS,EAAEL,SAAS,GAAG,mCAAmC,GAAG;sBAC/D,CAAE;sBAAApC,QAAA,eACAvG,OAAA;wBAAKsG,SAAS,EAAC,8DAA8D;wBAACkB,KAAK,EAAE;0BACnFM,KAAK,EAAE,SAAS;0BAChBmB,UAAU,EAAE,KAAK;0BACjBC,QAAQ,EAAE,QAAQ;0BAClBC,UAAU,EAAE;wBACd,CAAE;wBAAA5C,QAAA,EACCH,MAAM,CAACN,QAAQ,CAACsD,IAAI,IAAI,UAAU;sBAAC;wBAAA5C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGLb,QAAQ,CAACuD,KAAK,iBACbrJ,OAAA;oBAAKsG,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,eACvCvG,OAAA;sBAAKsG,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,eAC7FvG,OAAA;wBACEsJ,GAAG,EAAExD,QAAQ,CAACuD,KAAM;wBACpBE,GAAG,EAAC,oBAAoB;wBACxBjD,SAAS,EAAC;sBAA4C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,eAGD3G,OAAA;oBAAKsG,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBAErCvG,OAAA;sBAAAuG,QAAA,gBACEvG,OAAA;wBAAIsG,SAAS,EAAC,2EAA2E;wBAAAC,QAAA,gBACvFvG,OAAA;0BAAMsG,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAC;wBAAE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,gBAE3C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL3G,OAAA;wBAAKsG,SAAS,EAAG,2GACfqC,SAAS,GACL,6CAA6C,GAC7C,uCACL,EAAE;wBAACnB,KAAK,EAAE;0BACTuB,MAAM,EAAEJ,SAAS,GAAG,mBAAmB,GAAG,mBAAmB;0BAC7DK,SAAS,EAAEL,SAAS,GAAG,mCAAmC,GAAG;wBAC/D,CAAE;wBAAApC,QAAA,gBACAvG,OAAA;0BAAKsG,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,EAChCoC,SAAS,gBACR3I,OAAA;4BAAKsG,SAAS,EAAC,4FAA4F;4BAAAC,QAAA,eACzGvG,OAAA,CAACd,OAAO;8BAACoH,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrD,CAAC,gBAEN3G,OAAA;4BAAKsG,SAAS,EAAC,0FAA0F;4BAAAC,QAAA,eACvGvG,OAAA,CAACb,GAAG;8BAACmH,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjD;wBACN;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN3G,OAAA;0BAAKsG,SAAS,EAAC,QAAQ;0BAAAC,QAAA,gBACrBvG,OAAA;4BAAKsG,SAAS,EAAC,iCAAiC;4BAACkB,KAAK,EAAE;8BACtDM,KAAK,EAAE,SAAS;8BAChBmB,UAAU,EAAE,KAAK;8BACjBC,QAAQ,EAAE;4BACZ,CAAE;4BAAA3C,QAAA,EACCH,MAAM,CAACJ,UAAU,IAAI,oBAAoB;0BAAC;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CAAC,EACLgC,SAAS,iBACR3I,OAAA;4BAAGsG,SAAS,EAAC,oDAAoD;4BAAAC,QAAA,EAAC;0BAElE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CACJ,EACA,CAACgC,SAAS,IAAI3C,UAAU,iBACvBhG,OAAA;4BAAGsG,SAAS,EAAC,kDAAkD;4BAAAC,QAAA,EAAC;0BAEhE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CACJ;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGN3G,OAAA;sBAAAuG,QAAA,gBACEvG,OAAA;wBAAIsG,SAAS,EAAC,2EAA2E;wBAAAC,QAAA,gBACvFvG,OAAA;0BAAMsG,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAAC;wBAAC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,mBAE3C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL3G,OAAA;wBAAKsG,SAAS,EAAC,qJAAqJ;wBAACkB,KAAK,EAAE;0BAC1KuB,MAAM,EAAE,mBAAmB;0BAC3BC,SAAS,EAAE;wBACb,CAAE;wBAAAzC,QAAA,gBACAvG,OAAA;0BAAKsG,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,eACjCvG,OAAA;4BAAKsG,SAAS,EAAC,4FAA4F;4BAAAC,QAAA,eACzGvG,OAAA,CAACd,OAAO;8BAACoH,SAAS,EAAC;4BAAkC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACN3G,OAAA;0BAAKsG,SAAS,EAAC,QAAQ;0BAAAC,QAAA,gBACrBvG,OAAA;4BAAKsG,SAAS,EAAC,iCAAiC;4BAACkB,KAAK,EAAE;8BACtDM,KAAK,EAAE,SAAS;8BAChBmB,UAAU,EAAE,KAAK;8BACjBC,QAAQ,EAAE;4BACZ,CAAE;4BAAA3C,QAAA,EACCH,MAAM,CAACyC,aAAa,IAAI,KAAK;0BAAC;4BAAArC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5B,CAAC,eACN3G,OAAA;4BAAGsG,SAAS,EAAC,oDAAoD;4BAAAC,QAAA,EAAC;0BAElE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAGL,CAACgC,SAAS,iBACT3I,OAAA;sBAAKsG,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BvG,OAAA;wBACE4G,OAAO,EAAEA,CAAA,KAAMf,gBAAgB,CAC7BO,MAAM,CAACN,QAAQ,CAACsD,IAAI,IAAI,UAAU,CAAC,EACnChD,MAAM,CAACyC,aAAa,IAAI,KAAK,CAAC,EAC9BzC,MAAM,CAACJ,UAAU,IAAI,EAAE,CAAC,EACxBF,QAAQ,CAACuD,KACX,CAAE;wBACF/C,SAAS,EAAC,gTAAgT;wBAAAC,QAAA,gBAE1TvG,OAAA,CAACR,MAAM;0BAAC8G,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5C3G,OAAA;0BAAAuG,QAAA,EAAM;wBAAe;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC,EAER/F,YAAY,CAACwF,MAAM,CAACN,QAAQ,CAACsD,IAAI,IAAI,UAAU,CAAC,CAAC,iBAChDpJ,OAAA;wBAAKsG,SAAS,EAAC,0EAA0E;wBAACkB,KAAK,EAAE;0BAC/FuB,MAAM,EAAE,mBAAmB;0BAC3BC,SAAS,EAAE;wBACb,CAAE;wBAAAzC,QAAA,gBACAvG,OAAA;0BAAKsG,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1CvG,OAAA;4BAAKsG,SAAS,EAAC,6FAA6F;4BAAAC,QAAA,eAC1GvG,OAAA,CAACR,MAAM;8BAAC8G,SAAS,EAAC;4BAAoB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC,eACN3G,OAAA;4BAAIsG,SAAS,EAAC,4CAA4C;4BAAAC,QAAA,EAAC;0BAAY;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzE,CAAC,eACN3G,OAAA;0BAAKsG,SAAS,EAAC,gEAAgE;0BAACkB,KAAK,EAAE;4BACrFM,KAAK,EAAE,SAAS;4BAChBmB,UAAU,EAAE,KAAK;4BACjBE,UAAU,EAAE;0BACd,CAAE;0BAAA5C,QAAA,EACCH,MAAM,CAACxF,YAAY,CAACwF,MAAM,CAACN,QAAQ,CAACsD,IAAI,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE;wBAAC;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN,EAGAgC,SAAS,iBACR3I,OAAA;sBAAKsG,SAAS,EAAC,2DAA2D;sBAAAC,QAAA,eACxEvG,OAAA;wBAAKsG,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtCvG,OAAA;0BAAKsG,SAAS,EAAC,oEAAoE;0BAAAC,QAAA,eACjFvG,OAAA,CAACd,OAAO;4BAACoH,SAAS,EAAC;0BAAoB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvC,CAAC,eACN3G,OAAA;0BAAAuG,QAAA,gBACEvG,OAAA;4BAAIsG,SAAS,EAAC,+CAA+C;4BAAAC,QAAA,EAAC;0BAAU;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC7E3G,OAAA;4BAAGsG,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,EAAC;0BAEjD;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAjNE3C,KAAK;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkNV,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3G,OAAA;UAAKsG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFvG,OAAA;YACEsG,SAAS,EAAC,uVAAuV;YACjWM,OAAO,EAAEA,CAAA,KAAMzF,QAAQ,CAAE,cAAaD,EAAG,QAAO,CAAE;YAAAqF,QAAA,gBAElDvG,OAAA,CAACT,SAAS;cAAC+G,SAAS,EAAC;YAAqF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE/G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET3G,OAAA;YACEsG,SAAS,EAAC,uVAAuV;YACjWM,OAAO,EAAEA,CAAA,KAAMzF,QAAQ,CAAC,YAAY,CAAE;YAAAoF,QAAA,gBAEtCvG,OAAA,CAACX,OAAO;cAACiH,SAAS,EAAC;YAAoF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1G3G,OAAA;cAAMsG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD3G,OAAA;cAAMsG,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAACvG,EAAA,CA1xBID,UAAU;EAAA,QAOCxB,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW,EACFG,aAAa;AAAA;AAAAuK,EAAA,GAXnCrJ,UAAU;AA4xBhB,eAAeA,UAAU;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}