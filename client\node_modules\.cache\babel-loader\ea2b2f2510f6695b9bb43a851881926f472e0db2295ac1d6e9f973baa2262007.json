{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ErrorBoundary.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // Log the error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n\n    // Check if it's the specific \"Objects are not valid as a React child\" error\n    if (error.message && error.message.includes('Objects are not valid as a React child')) {\n      console.error('🚨 FOUND THE OBJECT RENDERING ERROR!');\n      console.error('This error is caused by trying to render an object directly in JSX');\n      console.error('Look for patterns like {question} instead of {question.name}');\n      console.error('Error stack:', error.stack);\n    }\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      // Fallback UI\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-red-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-lg p-6 max-w-lg w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-red-500 text-6xl mb-4 text-center\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4 text-center\",\n            children: \"Something went wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-600 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-2\",\n              children: \"An error occurred while rendering the page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), this.state.error && this.state.error.message.includes('Objects are not valid as a React child') && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-yellow-50 border border-yellow-200 rounded p-3 mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-yellow-800 font-medium\",\n                children: \"\\uD83D\\uDD0D Debug Info:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-yellow-700 text-sm mt-1\",\n                children: \"This error is caused by trying to render an object directly in JSX. Check the browser console for more details.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.reload(),\n            className: \"w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors\",\n            children: \"Reload Page\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"details\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              className: \"cursor-pointer text-sm text-gray-500\",\n              children: \"Show Error Details (Development)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto\",\n              children: [this.state.error && this.state.error.toString(), this.state.errorInfo.componentStack]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "getDerivedStateFromError", "componentDidCatch", "console", "message", "includes", "stack", "setState", "render", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "process", "env", "NODE_ENV", "toString", "componentStack"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ErrorBoundary.js"], "sourcesContent": ["import React from 'react';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log the error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    // Check if it's the specific \"Objects are not valid as a React child\" error\n    if (error.message && error.message.includes('Objects are not valid as a React child')) {\n      console.error('🚨 FOUND THE OBJECT RENDERING ERROR!');\n      console.error('This error is caused by trying to render an object directly in JSX');\n      console.error('Look for patterns like {question} instead of {question.name}');\n      console.error('Error stack:', error.stack);\n    }\n    \n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // Fallback UI\n      return (\n        <div className=\"min-h-screen bg-red-50 flex items-center justify-center p-4\">\n          <div className=\"bg-white rounded-lg shadow-lg p-6 max-w-lg w-full\">\n            <div className=\"text-red-500 text-6xl mb-4 text-center\">⚠️</div>\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4 text-center\">\n              Something went wrong\n            </h2>\n            <div className=\"text-gray-600 mb-4\">\n              <p className=\"mb-2\">An error occurred while rendering the page.</p>\n              {this.state.error && this.state.error.message.includes('Objects are not valid as a React child') && (\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded p-3 mt-3\">\n                  <p className=\"text-yellow-800 font-medium\">🔍 Debug Info:</p>\n                  <p className=\"text-yellow-700 text-sm mt-1\">\n                    This error is caused by trying to render an object directly in JSX. \n                    Check the browser console for more details.\n                  </p>\n                </div>\n              )}\n            </div>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors\"\n            >\n              Reload Page\n            </button>\n            \n            {process.env.NODE_ENV === 'development' && (\n              <details className=\"mt-4\">\n                <summary className=\"cursor-pointer text-sm text-gray-500\">\n                  Show Error Details (Development)\n                </summary>\n                <pre className=\"mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto\">\n                  {this.state.error && this.state.error.toString()}\n                  {this.state.errorInfo.componentStack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,SAASH,KAAK,CAACI,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;EAChE;EAEA,OAAOC,wBAAwBA,CAACF,KAAK,EAAE;IACrC;IACA,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAI,iBAAiBA,CAACH,KAAK,EAAEC,SAAS,EAAE;IAClC;IACAG,OAAO,CAACJ,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEC,SAAS,CAAC;;IAEjE;IACA,IAAID,KAAK,CAACK,OAAO,IAAIL,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,wCAAwC,CAAC,EAAE;MACrFF,OAAO,CAACJ,KAAK,CAAC,sCAAsC,CAAC;MACrDI,OAAO,CAACJ,KAAK,CAAC,oEAAoE,CAAC;MACnFI,OAAO,CAACJ,KAAK,CAAC,8DAA8D,CAAC;MAC7EI,OAAO,CAACJ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACO,KAAK,CAAC;IAC5C;IAEA,IAAI,CAACC,QAAQ,CAAC;MACZR,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EAEAQ,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACX,KAAK,CAACC,QAAQ,EAAE;MACvB;MACA,oBACEN,OAAA;QAAKiB,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1ElB,OAAA;UAAKiB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChElB,OAAA;YAAKiB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChEtB,OAAA;YAAIiB,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtB,OAAA;YAAKiB,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjClB,OAAA;cAAGiB,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAA2C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAClE,IAAI,CAACjB,KAAK,CAACE,KAAK,IAAI,IAAI,CAACF,KAAK,CAACE,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,wCAAwC,CAAC,iBAC9Fb,OAAA;cAAKiB,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACrElB,OAAA;gBAAGiB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7DtB,OAAA;gBAAGiB,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAG5C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNtB,OAAA;YACEuB,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCT,SAAS,EAAC,qFAAqF;YAAAC,QAAA,EAChG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC7B,OAAA;YAASiB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACvBlB,OAAA;cAASiB,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAE1D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACVtB,OAAA;cAAKiB,SAAS,EAAC,oDAAoD;cAAAC,QAAA,GAChE,IAAI,CAACb,KAAK,CAACE,KAAK,IAAI,IAAI,CAACF,KAAK,CAACE,KAAK,CAACuB,QAAQ,CAAC,CAAC,EAC/C,IAAI,CAACzB,KAAK,CAACG,SAAS,CAACuB,cAAc;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAAClB,KAAK,CAACc,QAAQ;EAC5B;AACF;AAEA,eAAejB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}