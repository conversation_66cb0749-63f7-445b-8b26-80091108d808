{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\ModernQuizPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { TbBrain, TbTrophy, TbClock, TbStar, TbBook, TbChevronRight } from 'react-icons/tb';\nimport QuizDashboard from '../../../components/modern/QuizDashboard';\nimport QuizInterface from '../../../components/modern/QuizInterface';\nimport { getAllQuizzes, getQuizById, submitQuizResult, getUserResults } from '../../../apicalls/quiz';\nimport Loading from '../../../components/modern/Loading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernQuizPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard', 'quiz', 'result'\n  const [quizzes, setQuizzes] = useState([]);\n  const [userResults, setUserResults] = useState({});\n  const [currentQuiz, setCurrentQuiz] = useState(null);\n  const [currentQuestions, setCurrentQuestions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [quizLoading, setQuizLoading] = useState(false);\n\n  // Load initial data\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Load quizzes and user results in parallel\n      const [quizzesResponse, resultsResponse] = await Promise.all([getAllQuizzes(), getUserResults()]);\n      if (quizzesResponse.success) {\n        setQuizzes(quizzesResponse.data || []);\n      } else {\n        // Fallback to demo data if API fails\n        setQuizzes([{\n          _id: 'demo1',\n          name: 'Mathematics Quiz - Algebra Basics',\n          subject: 'Mathematics',\n          duration: 30,\n          questions: Array(15).fill({}),\n          xpPoints: 150,\n          passingMarks: 60,\n          class: '7',\n          category: 'Practice Test'\n        }, {\n          _id: 'demo2',\n          name: 'Science Quiz - Physics Fundamentals',\n          subject: 'Physics',\n          duration: 45,\n          questions: Array(20).fill({}),\n          xpPoints: 200,\n          passingMarks: 70,\n          class: '8',\n          category: 'Chapter Test'\n        }, {\n          _id: 'demo3',\n          name: 'English Grammar and Comprehension',\n          subject: 'English',\n          duration: 25,\n          questions: Array(12).fill({}),\n          xpPoints: 120,\n          passingMarks: 60,\n          class: '7',\n          category: 'Weekly Test'\n        }, {\n          _id: 'demo4',\n          name: 'Chemistry - Periodic Table',\n          subject: 'Chemistry',\n          duration: 35,\n          questions: Array(18).fill({}),\n          xpPoints: 180,\n          passingMarks: 65,\n          class: '9',\n          category: 'Unit Test'\n        }, {\n          _id: 'demo5',\n          name: 'History - World War II',\n          subject: 'History',\n          duration: 40,\n          questions: Array(16).fill({}),\n          xpPoints: 160,\n          passingMarks: 60,\n          class: '10',\n          category: 'Chapter Test'\n        }]);\n      }\n      if (resultsResponse.success) {\n        // Convert results array to object with quiz ID as key\n        const resultsMap = {};\n        (resultsResponse.data || []).forEach(result => {\n          resultsMap[result.quiz] = result;\n        });\n        setUserResults(resultsMap);\n      } else {\n        // Demo results\n        setUserResults({\n          'demo1': {\n            percentage: 85,\n            correctAnswers: 13,\n            totalQuestions: 15,\n            xpEarned: 150,\n            completedAt: new Date().toISOString()\n          },\n          'demo2': {\n            percentage: 45,\n            correctAnswers: 9,\n            totalQuestions: 20,\n            xpEarned: 0,\n            completedAt: new Date(Date.now() - 86400000).toISOString() // Yesterday\n          }\n        });\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      // Set demo data on error\n      setQuizzes([{\n        _id: 'demo1',\n        name: 'Mathematics Quiz - Algebra Basics',\n        subject: 'Mathematics',\n        duration: 30,\n        questions: Array(15).fill({}),\n        xpPoints: 150,\n        passingMarks: 60,\n        class: '7',\n        category: 'Practice Test'\n      }, {\n        _id: 'demo2',\n        name: 'Science Quiz - Physics Fundamentals',\n        subject: 'Physics',\n        duration: 45,\n        questions: Array(20).fill({}),\n        xpPoints: 200,\n        passingMarks: 70,\n        class: '8',\n        category: 'Chapter Test'\n      }, {\n        _id: 'demo3',\n        name: 'English Grammar Test',\n        subject: 'English',\n        duration: 25,\n        questions: Array(12).fill({}),\n        xpPoints: 120,\n        passingMarks: 60,\n        class: '7',\n        category: 'Weekly Test'\n      }]);\n      setUserResults({\n        'demo1': {\n          percentage: 85,\n          correctAnswers: 13,\n          totalQuestions: 15,\n          xpEarned: 150,\n          completedAt: new Date().toISOString()\n        },\n        'demo3': {\n          percentage: 75,\n          correctAnswers: 9,\n          totalQuestions: 12,\n          xpEarned: 120,\n          completedAt: new Date(Date.now() - 172800000).toISOString() // 2 days ago\n        }\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle quiz start\n  const handleQuizStart = async quiz => {\n    try {\n      setQuizLoading(true);\n\n      // Get full quiz details with questions\n      const response = await getQuizById(quiz._id);\n      if (response.success && response.data) {\n        setCurrentQuiz(response.data);\n        setCurrentQuestions(response.data.questions || []);\n        setCurrentView('quiz');\n      } else {\n        // Demo questions for testing\n        const demoQuestions = [{\n          _id: 'q1',\n          name: 'What is the value of x in the equation 2x + 5 = 15?',\n          type: 'Options',\n          answerType: 'Options',\n          questionType: 'Options',\n          options: {\n            A: 'x = 5',\n            B: 'x = 10',\n            C: 'x = 7.5',\n            D: 'x = 2.5'\n          },\n          correctAnswer: 'A',\n          correctOption: 'A'\n        }, {\n          _id: 'q2',\n          name: 'Solve for y: 3y - 7 = 14',\n          type: 'Options',\n          answerType: 'Options',\n          questionType: 'Options',\n          options: {\n            A: 'y = 7',\n            B: 'y = 21',\n            C: 'y = 5',\n            D: 'y = 3'\n          },\n          correctAnswer: 'A',\n          correctOption: 'A'\n        }, {\n          _id: 'q3',\n          name: 'What is the chemical symbol for Gold?',\n          type: 'Fill in the Blank',\n          answerType: 'Fill in the Blank',\n          questionType: 'Fill in the Blank',\n          options: {},\n          correctAnswer: 'Au'\n        }, {\n          _id: 'q4',\n          name: 'Which planet is known as the Red Planet?',\n          type: 'Options',\n          answerType: 'Options',\n          questionType: 'Options',\n          options: {\n            A: 'Mars',\n            B: 'Venus',\n            C: 'Jupiter',\n            D: 'Saturn'\n          },\n          correctAnswer: 'A',\n          correctOption: 'A'\n        }, {\n          _id: 'q5',\n          name: 'What geometric shape is shown in the image below?',\n          type: 'picture_based',\n          answerType: 'Options',\n          questionType: 'picture_based',\n          imageUrl: 'https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=Triangle',\n          options: {\n            A: 'Triangle',\n            B: 'Square',\n            C: 'Circle',\n            D: 'Rectangle'\n          },\n          correctAnswer: 'A',\n          correctOption: 'A'\n        }];\n\n        // Validate demo questions before setting\n        const validatedQuestions = demoQuestions.filter(q => q && q._id && q.name);\n        console.log('Setting demo questions:', validatedQuestions);\n        setCurrentQuiz(quiz);\n        setCurrentQuestions(validatedQuestions);\n        setCurrentView('quiz');\n      }\n    } catch (error) {\n      console.error('Error starting quiz:', error);\n      // Demo questions on error\n      const demoQuestions = [{\n        _id: 'q1',\n        name: 'What is the value of x in the equation 2x + 5 = 15?',\n        type: 'Options',\n        answerType: 'Options',\n        questionType: 'Options',\n        options: {\n          A: 'x = 5',\n          B: 'x = 10',\n          C: 'x = 7.5',\n          D: 'x = 2.5'\n        },\n        correctAnswer: 'A',\n        correctOption: 'A'\n      }, {\n        _id: 'q2',\n        name: 'What is the capital of France?',\n        type: 'Fill in the Blank',\n        answerType: 'Fill in the Blank',\n        questionType: 'Fill in the Blank',\n        options: {},\n        correctAnswer: 'Paris'\n      }];\n\n      // Validate demo questions before setting\n      const validatedQuestions = demoQuestions.filter(q => q && q._id && q.name);\n      console.log('Setting error demo questions:', validatedQuestions);\n      setCurrentQuiz(quiz);\n      setCurrentQuestions(validatedQuestions);\n      setCurrentView('quiz');\n    } finally {\n      setQuizLoading(false);\n    }\n  };\n\n  // Handle quiz submission\n  const handleQuizSubmit = async answers => {\n    try {\n      setQuizLoading(true);\n\n      // Prepare submission data\n      const submissionData = {\n        quizId: currentQuiz._id,\n        answers: Object.entries(answers).map(([questionId, answer]) => ({\n          questionId,\n          answer\n        }))\n      };\n      const response = await submitQuizResult(submissionData);\n      if (response.success) {\n        message.success('Quiz submitted successfully!');\n\n        // Refresh dashboard data to show updated results\n        await loadDashboardData();\n\n        // Navigate to results page\n        navigate(`/quiz/${currentQuiz._id}/result`);\n      } else {\n        message.error(response.message || 'Failed to submit quiz');\n      }\n    } catch (error) {\n      console.error('Error submitting quiz:', error);\n      message.error('Failed to submit quiz');\n    } finally {\n      setQuizLoading(false);\n    }\n  };\n\n  // Handle back to dashboard\n  const handleBackToDashboard = () => {\n    setCurrentView('dashboard');\n    setCurrentQuiz(null);\n    setCurrentQuestions([]);\n  };\n\n  // Render loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(Loading, {\n          size: \"lg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading your quizzes...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render quiz interface\n  if (currentView === 'quiz' && currentQuiz) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [quizLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-6 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(Loading, {\n            size: \"lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-gray-600\",\n            children: \"Processing your quiz...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this), Array.isArray(currentQuestions) && currentQuestions.length > 0 ? /*#__PURE__*/_jsxDEV(QuizInterface, {\n        quiz: currentQuiz,\n        questions: currentQuestions,\n        onSubmit: handleQuizSubmit,\n        onExit: handleBackToDashboard\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-6xl mb-4\",\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-2\",\n            children: \"No Questions Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-4\",\n            children: \"This quiz doesn't have any questions yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBackToDashboard,\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render dashboard\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-blue-800 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-16 h-16 text-blue-200 mr-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl lg:text-6xl font-bold\",\n              children: [\"Brain\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-200\",\n                children: \"Wave\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 22\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl lg:text-2xl text-blue-100 mb-8\",\n            children: \"Challenge your brain, Beat the rest\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center gap-8 text-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Track Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Earn XP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbBook, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Learn & Grow\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QuizDashboard, {\n      quizzes: quizzes,\n      userResults: userResults,\n      onQuizStart: handleQuizStart,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"p-6 bg-blue-50 rounded-xl\",\n            children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-12 h-12 text-blue-600 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-2\",\n              children: \"Smart Learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"AI-powered questions adapted to your learning pace\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"p-6 bg-green-50 rounded-xl\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-12 h-12 text-green-600 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-2\",\n              children: \"Track Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Monitor your improvement with detailed analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"p-6 bg-purple-50 rounded-xl\",\n            children: [/*#__PURE__*/_jsxDEV(TbStar, {\n              className: \"w-12 h-12 text-purple-600 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-2\",\n              children: \"Earn Rewards\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Collect XP points and unlock achievements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 424,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernQuizPage, \"SH04bEwbtKhiKPlKYMycvnlI4gQ=\", false, function () {\n  return [useNavigate];\n});\n_c = ModernQuizPage;\nexport default ModernQuizPage;\nvar _c;\n$RefreshReg$(_c, \"ModernQuizPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "motion", "message", "TbBrain", "TbTrophy", "TbClock", "TbStar", "TbBook", "TbChevronRight", "QuizDashboard", "QuizInterface", "getAllQuizzes", "getQuizById", "submitQuizResult", "getUserResults", "Loading", "jsxDEV", "_jsxDEV", "ModernQuizPage", "_s", "navigate", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "quizzes", "setQuizzes", "userResults", "setUserResults", "currentQuiz", "setCurrentQuiz", "currentQuestions", "setCurrentQuestions", "loading", "setLoading", "quizLoading", "setQuizLoading", "loadDashboardData", "quizzesResponse", "resultsResponse", "Promise", "all", "success", "data", "_id", "name", "subject", "duration", "questions", "Array", "fill", "xpPoints", "passingMarks", "class", "category", "resultsMap", "for<PERSON>ach", "result", "quiz", "percentage", "correctAnswers", "totalQuestions", "xpEarned", "completedAt", "Date", "toISOString", "now", "error", "console", "handleQuizStart", "response", "demoQuestions", "type", "answerType", "questionType", "options", "A", "B", "C", "D", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "imageUrl", "validatedQuestions", "filter", "q", "log", "handleQuizSubmit", "answers", "submissionData", "quizId", "Object", "entries", "map", "questionId", "answer", "handleBackToDashboard", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isArray", "length", "onSubmit", "onExit", "onClick", "div", "initial", "opacity", "y", "animate", "transition", "onQuizStart", "whileHover", "scale", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/ModernQuizPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { \n  TbBrain,\n  TbTrophy,\n  TbClock,\n  TbStar,\n  TbBook,\n  TbChevronRight\n} from 'react-icons/tb';\nimport QuizDashboard from '../../../components/modern/QuizDashboard';\nimport QuizInterface from '../../../components/modern/QuizInterface';\nimport { getAllQuizzes, getQuizById, submitQuizResult, getUserResults } from '../../../apicalls/quiz';\nimport Loading from '../../../components/modern/Loading';\n\nconst ModernQuizPage = () => {\n  const navigate = useNavigate();\n  const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard', 'quiz', 'result'\n  const [quizzes, setQuizzes] = useState([]);\n  const [userResults, setUserResults] = useState({});\n  const [currentQuiz, setCurrentQuiz] = useState(null);\n  const [currentQuestions, setCurrentQuestions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [quizLoading, setQuizLoading] = useState(false);\n\n  // Load initial data\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Load quizzes and user results in parallel\n      const [quizzesResponse, resultsResponse] = await Promise.all([\n        getAllQuizzes(),\n        getUserResults()\n      ]);\n\n      if (quizzesResponse.success) {\n        setQuizzes(quizzesResponse.data || []);\n      } else {\n        // Fallback to demo data if API fails\n        setQuizzes([\n          {\n            _id: 'demo1',\n            name: 'Mathematics Quiz - Algebra Basics',\n            subject: 'Mathematics',\n            duration: 30,\n            questions: Array(15).fill({}),\n            xpPoints: 150,\n            passingMarks: 60,\n            class: '7',\n            category: 'Practice Test'\n          },\n          {\n            _id: 'demo2',\n            name: 'Science Quiz - Physics Fundamentals',\n            subject: 'Physics',\n            duration: 45,\n            questions: Array(20).fill({}),\n            xpPoints: 200,\n            passingMarks: 70,\n            class: '8',\n            category: 'Chapter Test'\n          },\n          {\n            _id: 'demo3',\n            name: 'English Grammar and Comprehension',\n            subject: 'English',\n            duration: 25,\n            questions: Array(12).fill({}),\n            xpPoints: 120,\n            passingMarks: 60,\n            class: '7',\n            category: 'Weekly Test'\n          },\n          {\n            _id: 'demo4',\n            name: 'Chemistry - Periodic Table',\n            subject: 'Chemistry',\n            duration: 35,\n            questions: Array(18).fill({}),\n            xpPoints: 180,\n            passingMarks: 65,\n            class: '9',\n            category: 'Unit Test'\n          },\n          {\n            _id: 'demo5',\n            name: 'History - World War II',\n            subject: 'History',\n            duration: 40,\n            questions: Array(16).fill({}),\n            xpPoints: 160,\n            passingMarks: 60,\n            class: '10',\n            category: 'Chapter Test'\n          }\n        ]);\n      }\n\n      if (resultsResponse.success) {\n        // Convert results array to object with quiz ID as key\n        const resultsMap = {};\n        (resultsResponse.data || []).forEach(result => {\n          resultsMap[result.quiz] = result;\n        });\n        setUserResults(resultsMap);\n      } else {\n        // Demo results\n        setUserResults({\n          'demo1': {\n            percentage: 85,\n            correctAnswers: 13,\n            totalQuestions: 15,\n            xpEarned: 150,\n            completedAt: new Date().toISOString()\n          },\n          'demo2': {\n            percentage: 45,\n            correctAnswers: 9,\n            totalQuestions: 20,\n            xpEarned: 0,\n            completedAt: new Date(Date.now() - 86400000).toISOString() // Yesterday\n          }\n        });\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      // Set demo data on error\n      setQuizzes([\n        {\n          _id: 'demo1',\n          name: 'Mathematics Quiz - Algebra Basics',\n          subject: 'Mathematics',\n          duration: 30,\n          questions: Array(15).fill({}),\n          xpPoints: 150,\n          passingMarks: 60,\n          class: '7',\n          category: 'Practice Test'\n        },\n        {\n          _id: 'demo2',\n          name: 'Science Quiz - Physics Fundamentals',\n          subject: 'Physics',\n          duration: 45,\n          questions: Array(20).fill({}),\n          xpPoints: 200,\n          passingMarks: 70,\n          class: '8',\n          category: 'Chapter Test'\n        },\n        {\n          _id: 'demo3',\n          name: 'English Grammar Test',\n          subject: 'English',\n          duration: 25,\n          questions: Array(12).fill({}),\n          xpPoints: 120,\n          passingMarks: 60,\n          class: '7',\n          category: 'Weekly Test'\n        }\n      ]);\n      setUserResults({\n        'demo1': {\n          percentage: 85,\n          correctAnswers: 13,\n          totalQuestions: 15,\n          xpEarned: 150,\n          completedAt: new Date().toISOString()\n        },\n        'demo3': {\n          percentage: 75,\n          correctAnswers: 9,\n          totalQuestions: 12,\n          xpEarned: 120,\n          completedAt: new Date(Date.now() - 172800000).toISOString() // 2 days ago\n        }\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle quiz start\n  const handleQuizStart = async (quiz) => {\n    try {\n      setQuizLoading(true);\n\n      // Get full quiz details with questions\n      const response = await getQuizById(quiz._id);\n\n      if (response.success && response.data) {\n        setCurrentQuiz(response.data);\n        setCurrentQuestions(response.data.questions || []);\n        setCurrentView('quiz');\n      } else {\n        // Demo questions for testing\n        const demoQuestions = [\n          {\n            _id: 'q1',\n            name: 'What is the value of x in the equation 2x + 5 = 15?',\n            type: 'Options',\n            answerType: 'Options',\n            questionType: 'Options',\n            options: {\n              A: 'x = 5',\n              B: 'x = 10',\n              C: 'x = 7.5',\n              D: 'x = 2.5'\n            },\n            correctAnswer: 'A',\n            correctOption: 'A'\n          },\n          {\n            _id: 'q2',\n            name: 'Solve for y: 3y - 7 = 14',\n            type: 'Options',\n            answerType: 'Options',\n            questionType: 'Options',\n            options: {\n              A: 'y = 7',\n              B: 'y = 21',\n              C: 'y = 5',\n              D: 'y = 3'\n            },\n            correctAnswer: 'A',\n            correctOption: 'A'\n          },\n          {\n            _id: 'q3',\n            name: 'What is the chemical symbol for Gold?',\n            type: 'Fill in the Blank',\n            answerType: 'Fill in the Blank',\n            questionType: 'Fill in the Blank',\n            options: {},\n            correctAnswer: 'Au'\n          },\n          {\n            _id: 'q4',\n            name: 'Which planet is known as the Red Planet?',\n            type: 'Options',\n            answerType: 'Options',\n            questionType: 'Options',\n            options: {\n              A: 'Mars',\n              B: 'Venus',\n              C: 'Jupiter',\n              D: 'Saturn'\n            },\n            correctAnswer: 'A',\n            correctOption: 'A'\n          },\n          {\n            _id: 'q5',\n            name: 'What geometric shape is shown in the image below?',\n            type: 'picture_based',\n            answerType: 'Options',\n            questionType: 'picture_based',\n            imageUrl: 'https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=Triangle',\n            options: {\n              A: 'Triangle',\n              B: 'Square',\n              C: 'Circle',\n              D: 'Rectangle'\n            },\n            correctAnswer: 'A',\n            correctOption: 'A'\n          }\n        ];\n\n        // Validate demo questions before setting\n        const validatedQuestions = demoQuestions.filter(q => q && q._id && q.name);\n        console.log('Setting demo questions:', validatedQuestions);\n\n        setCurrentQuiz(quiz);\n        setCurrentQuestions(validatedQuestions);\n        setCurrentView('quiz');\n      }\n    } catch (error) {\n      console.error('Error starting quiz:', error);\n      // Demo questions on error\n      const demoQuestions = [\n        {\n          _id: 'q1',\n          name: 'What is the value of x in the equation 2x + 5 = 15?',\n          type: 'Options',\n          answerType: 'Options',\n          questionType: 'Options',\n          options: {\n            A: 'x = 5',\n            B: 'x = 10',\n            C: 'x = 7.5',\n            D: 'x = 2.5'\n          },\n          correctAnswer: 'A',\n          correctOption: 'A'\n        },\n        {\n          _id: 'q2',\n          name: 'What is the capital of France?',\n          type: 'Fill in the Blank',\n          answerType: 'Fill in the Blank',\n          questionType: 'Fill in the Blank',\n          options: {},\n          correctAnswer: 'Paris'\n        }\n      ];\n\n      // Validate demo questions before setting\n      const validatedQuestions = demoQuestions.filter(q => q && q._id && q.name);\n      console.log('Setting error demo questions:', validatedQuestions);\n\n      setCurrentQuiz(quiz);\n      setCurrentQuestions(validatedQuestions);\n      setCurrentView('quiz');\n    } finally {\n      setQuizLoading(false);\n    }\n  };\n\n  // Handle quiz submission\n  const handleQuizSubmit = async (answers) => {\n    try {\n      setQuizLoading(true);\n      \n      // Prepare submission data\n      const submissionData = {\n        quizId: currentQuiz._id,\n        answers: Object.entries(answers).map(([questionId, answer]) => ({\n          questionId,\n          answer\n        }))\n      };\n\n      const response = await submitQuizResult(submissionData);\n      \n      if (response.success) {\n        message.success('Quiz submitted successfully!');\n        \n        // Refresh dashboard data to show updated results\n        await loadDashboardData();\n        \n        // Navigate to results page\n        navigate(`/quiz/${currentQuiz._id}/result`);\n      } else {\n        message.error(response.message || 'Failed to submit quiz');\n      }\n    } catch (error) {\n      console.error('Error submitting quiz:', error);\n      message.error('Failed to submit quiz');\n    } finally {\n      setQuizLoading(false);\n    }\n  };\n\n  // Handle back to dashboard\n  const handleBackToDashboard = () => {\n    setCurrentView('dashboard');\n    setCurrentQuiz(null);\n    setCurrentQuestions([]);\n  };\n\n  // Render loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loading size=\"lg\" />\n          <p className=\"mt-4 text-gray-600\">Loading your quizzes...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Render quiz interface\n  if (currentView === 'quiz' && currentQuiz) {\n    return (\n      <div className=\"relative\">\n        {quizLoading && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <div className=\"bg-white rounded-lg p-6 text-center\">\n              <Loading size=\"lg\" />\n              <p className=\"mt-4 text-gray-600\">Processing your quiz...</p>\n            </div>\n          </div>\n        )}\n        \n        {/* Add error boundary around QuizInterface */}\n        {Array.isArray(currentQuestions) && currentQuestions.length > 0 ? (\n          <QuizInterface\n            quiz={currentQuiz}\n            questions={currentQuestions}\n            onSubmit={handleQuizSubmit}\n            onExit={handleBackToDashboard}\n          />\n        ) : (\n          <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <div className=\"text-6xl mb-4\">📝</div>\n              <h2 className=\"text-xl font-bold text-gray-900 mb-2\">No Questions Available</h2>\n              <p className=\"text-gray-600 mb-4\">This quiz doesn't have any questions yet.</p>\n              <button\n                onClick={handleBackToDashboard}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Back to Dashboard\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  // Render dashboard\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center\"\n          >\n            <div className=\"flex items-center justify-center mb-6\">\n              <TbBrain className=\"w-16 h-16 text-blue-200 mr-4\" />\n              <h1 className=\"text-4xl lg:text-6xl font-bold\">\n                Brain<span className=\"text-blue-200\">Wave</span>\n              </h1>\n            </div>\n            <p className=\"text-xl lg:text-2xl text-blue-100 mb-8\">\n              Challenge your brain, Beat the rest\n            </p>\n            <div className=\"flex items-center justify-center gap-8 text-blue-100\">\n              <div className=\"flex items-center gap-2\">\n                <TbTrophy className=\"w-6 h-6\" />\n                <span>Track Progress</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <TbStar className=\"w-6 h-6\" />\n                <span>Earn XP</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <TbBook className=\"w-6 h-6\" />\n                <span>Learn & Grow</span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Dashboard */}\n      <QuizDashboard\n        quizzes={quizzes}\n        userResults={userResults}\n        onQuizStart={handleQuizStart}\n        loading={loading}\n      />\n\n      {/* Quick Stats Footer */}\n      <div className=\"bg-white border-t\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"p-6 bg-blue-50 rounded-xl\"\n            >\n              <TbBrain className=\"w-12 h-12 text-blue-600 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Smart Learning</h3>\n              <p className=\"text-gray-600\">AI-powered questions adapted to your learning pace</p>\n            </motion.div>\n            \n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"p-6 bg-green-50 rounded-xl\"\n            >\n              <TbTrophy className=\"w-12 h-12 text-green-600 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Track Progress</h3>\n              <p className=\"text-gray-600\">Monitor your improvement with detailed analytics</p>\n            </motion.div>\n            \n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"p-6 bg-purple-50 rounded-xl\"\n            >\n              <TbStar className=\"w-12 h-12 text-purple-600 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Earn Rewards</h3>\n              <p className=\"text-gray-600\">Collect XP points and unlock achievements</p>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ModernQuizPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,cAAc,QACT,gBAAgB;AACvB,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,SAASC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,wBAAwB;AACrG,OAAOC,OAAO,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACdoC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACI,eAAe,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3D5B,aAAa,CAAC,CAAC,EACfG,cAAc,CAAC,CAAC,CACjB,CAAC;MAEF,IAAIsB,eAAe,CAACI,OAAO,EAAE;QAC3BhB,UAAU,CAACY,eAAe,CAACK,IAAI,IAAI,EAAE,CAAC;MACxC,CAAC,MAAM;QACL;QACAjB,UAAU,CAAC,CACT;UACEkB,GAAG,EAAE,OAAO;UACZC,IAAI,EAAE,mCAAmC;UACzCC,OAAO,EAAE,aAAa;UACtBC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAEC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;UAC7BC,QAAQ,EAAE,GAAG;UACbC,YAAY,EAAE,EAAE;UAChBC,KAAK,EAAE,GAAG;UACVC,QAAQ,EAAE;QACZ,CAAC,EACD;UACEV,GAAG,EAAE,OAAO;UACZC,IAAI,EAAE,qCAAqC;UAC3CC,OAAO,EAAE,SAAS;UAClBC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAEC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;UAC7BC,QAAQ,EAAE,GAAG;UACbC,YAAY,EAAE,EAAE;UAChBC,KAAK,EAAE,GAAG;UACVC,QAAQ,EAAE;QACZ,CAAC,EACD;UACEV,GAAG,EAAE,OAAO;UACZC,IAAI,EAAE,mCAAmC;UACzCC,OAAO,EAAE,SAAS;UAClBC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAEC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;UAC7BC,QAAQ,EAAE,GAAG;UACbC,YAAY,EAAE,EAAE;UAChBC,KAAK,EAAE,GAAG;UACVC,QAAQ,EAAE;QACZ,CAAC,EACD;UACEV,GAAG,EAAE,OAAO;UACZC,IAAI,EAAE,4BAA4B;UAClCC,OAAO,EAAE,WAAW;UACpBC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAEC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;UAC7BC,QAAQ,EAAE,GAAG;UACbC,YAAY,EAAE,EAAE;UAChBC,KAAK,EAAE,GAAG;UACVC,QAAQ,EAAE;QACZ,CAAC,EACD;UACEV,GAAG,EAAE,OAAO;UACZC,IAAI,EAAE,wBAAwB;UAC9BC,OAAO,EAAE,SAAS;UAClBC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAEC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;UAC7BC,QAAQ,EAAE,GAAG;UACbC,YAAY,EAAE,EAAE;UAChBC,KAAK,EAAE,IAAI;UACXC,QAAQ,EAAE;QACZ,CAAC,CACF,CAAC;MACJ;MAEA,IAAIf,eAAe,CAACG,OAAO,EAAE;QAC3B;QACA,MAAMa,UAAU,GAAG,CAAC,CAAC;QACrB,CAAChB,eAAe,CAACI,IAAI,IAAI,EAAE,EAAEa,OAAO,CAACC,MAAM,IAAI;UAC7CF,UAAU,CAACE,MAAM,CAACC,IAAI,CAAC,GAAGD,MAAM;QAClC,CAAC,CAAC;QACF7B,cAAc,CAAC2B,UAAU,CAAC;MAC5B,CAAC,MAAM;QACL;QACA3B,cAAc,CAAC;UACb,OAAO,EAAE;YACP+B,UAAU,EAAE,EAAE;YACdC,cAAc,EAAE,EAAE;YAClBC,cAAc,EAAE,EAAE;YAClBC,QAAQ,EAAE,GAAG;YACbC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACtC,CAAC;UACD,OAAO,EAAE;YACPN,UAAU,EAAE,EAAE;YACdC,cAAc,EAAE,CAAC;YACjBC,cAAc,EAAE,EAAE;YAClBC,QAAQ,EAAE,CAAC;YACXC,WAAW,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACD,WAAW,CAAC,CAAC,CAAC;UAC7D;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACAzC,UAAU,CAAC,CACT;QACEkB,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,mCAAmC;QACzCC,OAAO,EAAE,aAAa;QACtBC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAEC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7BC,QAAQ,EAAE,GAAG;QACbC,YAAY,EAAE,EAAE;QAChBC,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEV,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,qCAAqC;QAC3CC,OAAO,EAAE,SAAS;QAClBC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAEC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7BC,QAAQ,EAAE,GAAG;QACbC,YAAY,EAAE,EAAE;QAChBC,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEV,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,sBAAsB;QAC5BC,OAAO,EAAE,SAAS;QAClBC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAEC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7BC,QAAQ,EAAE,GAAG;QACbC,YAAY,EAAE,EAAE;QAChBC,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE;MACZ,CAAC,CACF,CAAC;MACF1B,cAAc,CAAC;QACb,OAAO,EAAE;UACP+B,UAAU,EAAE,EAAE;UACdC,cAAc,EAAE,EAAE;UAClBC,cAAc,EAAE,EAAE;UAClBC,QAAQ,EAAE,GAAG;UACbC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACtC,CAAC;QACD,OAAO,EAAE;UACPN,UAAU,EAAE,EAAE;UACdC,cAAc,EAAE,CAAC;UACjBC,cAAc,EAAE,EAAE;UAClBC,QAAQ,EAAE,GAAG;UACbC,WAAW,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACD,WAAW,CAAC,CAAC,CAAC;QAC9D;MACF,CAAC,CAAC;IACJ,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,eAAe,GAAG,MAAOX,IAAI,IAAK;IACtC,IAAI;MACFtB,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAMkC,QAAQ,GAAG,MAAMxD,WAAW,CAAC4C,IAAI,CAACd,GAAG,CAAC;MAE5C,IAAI0B,QAAQ,CAAC5B,OAAO,IAAI4B,QAAQ,CAAC3B,IAAI,EAAE;QACrCb,cAAc,CAACwC,QAAQ,CAAC3B,IAAI,CAAC;QAC7BX,mBAAmB,CAACsC,QAAQ,CAAC3B,IAAI,CAACK,SAAS,IAAI,EAAE,CAAC;QAClDxB,cAAc,CAAC,MAAM,CAAC;MACxB,CAAC,MAAM;QACL;QACA,MAAM+C,aAAa,GAAG,CACpB;UACE3B,GAAG,EAAE,IAAI;UACTC,IAAI,EAAE,qDAAqD;UAC3D2B,IAAI,EAAE,SAAS;UACfC,UAAU,EAAE,SAAS;UACrBC,YAAY,EAAE,SAAS;UACvBC,OAAO,EAAE;YACPC,CAAC,EAAE,OAAO;YACVC,CAAC,EAAE,QAAQ;YACXC,CAAC,EAAE,SAAS;YACZC,CAAC,EAAE;UACL,CAAC;UACDC,aAAa,EAAE,GAAG;UAClBC,aAAa,EAAE;QACjB,CAAC,EACD;UACErC,GAAG,EAAE,IAAI;UACTC,IAAI,EAAE,0BAA0B;UAChC2B,IAAI,EAAE,SAAS;UACfC,UAAU,EAAE,SAAS;UACrBC,YAAY,EAAE,SAAS;UACvBC,OAAO,EAAE;YACPC,CAAC,EAAE,OAAO;YACVC,CAAC,EAAE,QAAQ;YACXC,CAAC,EAAE,OAAO;YACVC,CAAC,EAAE;UACL,CAAC;UACDC,aAAa,EAAE,GAAG;UAClBC,aAAa,EAAE;QACjB,CAAC,EACD;UACErC,GAAG,EAAE,IAAI;UACTC,IAAI,EAAE,uCAAuC;UAC7C2B,IAAI,EAAE,mBAAmB;UACzBC,UAAU,EAAE,mBAAmB;UAC/BC,YAAY,EAAE,mBAAmB;UACjCC,OAAO,EAAE,CAAC,CAAC;UACXK,aAAa,EAAE;QACjB,CAAC,EACD;UACEpC,GAAG,EAAE,IAAI;UACTC,IAAI,EAAE,0CAA0C;UAChD2B,IAAI,EAAE,SAAS;UACfC,UAAU,EAAE,SAAS;UACrBC,YAAY,EAAE,SAAS;UACvBC,OAAO,EAAE;YACPC,CAAC,EAAE,MAAM;YACTC,CAAC,EAAE,OAAO;YACVC,CAAC,EAAE,SAAS;YACZC,CAAC,EAAE;UACL,CAAC;UACDC,aAAa,EAAE,GAAG;UAClBC,aAAa,EAAE;QACjB,CAAC,EACD;UACErC,GAAG,EAAE,IAAI;UACTC,IAAI,EAAE,mDAAmD;UACzD2B,IAAI,EAAE,eAAe;UACrBC,UAAU,EAAE,SAAS;UACrBC,YAAY,EAAE,eAAe;UAC7BQ,QAAQ,EAAE,iEAAiE;UAC3EP,OAAO,EAAE;YACPC,CAAC,EAAE,UAAU;YACbC,CAAC,EAAE,QAAQ;YACXC,CAAC,EAAE,QAAQ;YACXC,CAAC,EAAE;UACL,CAAC;UACDC,aAAa,EAAE,GAAG;UAClBC,aAAa,EAAE;QACjB,CAAC,CACF;;QAED;QACA,MAAME,kBAAkB,GAAGZ,aAAa,CAACa,MAAM,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACzC,GAAG,IAAIyC,CAAC,CAACxC,IAAI,CAAC;QAC1EuB,OAAO,CAACkB,GAAG,CAAC,yBAAyB,EAAEH,kBAAkB,CAAC;QAE1DrD,cAAc,CAAC4B,IAAI,CAAC;QACpB1B,mBAAmB,CAACmD,kBAAkB,CAAC;QACvC3D,cAAc,CAAC,MAAM,CAAC;MACxB;IACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACA,MAAMI,aAAa,GAAG,CACpB;QACE3B,GAAG,EAAE,IAAI;QACTC,IAAI,EAAE,qDAAqD;QAC3D2B,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,SAAS;QACrBC,YAAY,EAAE,SAAS;QACvBC,OAAO,EAAE;UACPC,CAAC,EAAE,OAAO;UACVC,CAAC,EAAE,QAAQ;UACXC,CAAC,EAAE,SAAS;UACZC,CAAC,EAAE;QACL,CAAC;QACDC,aAAa,EAAE,GAAG;QAClBC,aAAa,EAAE;MACjB,CAAC,EACD;QACErC,GAAG,EAAE,IAAI;QACTC,IAAI,EAAE,gCAAgC;QACtC2B,IAAI,EAAE,mBAAmB;QACzBC,UAAU,EAAE,mBAAmB;QAC/BC,YAAY,EAAE,mBAAmB;QACjCC,OAAO,EAAE,CAAC,CAAC;QACXK,aAAa,EAAE;MACjB,CAAC,CACF;;MAED;MACA,MAAMG,kBAAkB,GAAGZ,aAAa,CAACa,MAAM,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACzC,GAAG,IAAIyC,CAAC,CAACxC,IAAI,CAAC;MAC1EuB,OAAO,CAACkB,GAAG,CAAC,+BAA+B,EAAEH,kBAAkB,CAAC;MAEhErD,cAAc,CAAC4B,IAAI,CAAC;MACpB1B,mBAAmB,CAACmD,kBAAkB,CAAC;MACvC3D,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,SAAS;MACRY,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMmD,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAI;MACFpD,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAMqD,cAAc,GAAG;QACrBC,MAAM,EAAE7D,WAAW,CAACe,GAAG;QACvB4C,OAAO,EAAEG,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,UAAU,EAAEC,MAAM,CAAC,MAAM;UAC9DD,UAAU;UACVC;QACF,CAAC,CAAC;MACJ,CAAC;MAED,MAAMzB,QAAQ,GAAG,MAAMvD,gBAAgB,CAAC0E,cAAc,CAAC;MAEvD,IAAInB,QAAQ,CAAC5B,OAAO,EAAE;QACpBtC,OAAO,CAACsC,OAAO,CAAC,8BAA8B,CAAC;;QAE/C;QACA,MAAML,iBAAiB,CAAC,CAAC;;QAEzB;QACAf,QAAQ,CAAE,SAAQO,WAAW,CAACe,GAAI,SAAQ,CAAC;MAC7C,CAAC,MAAM;QACLxC,OAAO,CAAC+D,KAAK,CAACG,QAAQ,CAAClE,OAAO,IAAI,uBAAuB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C/D,OAAO,CAAC+D,KAAK,CAAC,uBAAuB,CAAC;IACxC,CAAC,SAAS;MACR/B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM4D,qBAAqB,GAAGA,CAAA,KAAM;IAClCxE,cAAc,CAAC,WAAW,CAAC;IAC3BM,cAAc,CAAC,IAAI,CAAC;IACpBE,mBAAmB,CAAC,EAAE,CAAC;EACzB,CAAC;;EAED;EACA,IAAIC,OAAO,EAAE;IACX,oBACEd,OAAA;MAAK8E,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvE/E,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B/E,OAAA,CAACF,OAAO;UAACkF,IAAI,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrBpF,OAAA;UAAG8E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIhF,WAAW,KAAK,MAAM,IAAIM,WAAW,EAAE;IACzC,oBACEV,OAAA;MAAK8E,SAAS,EAAC,UAAU;MAAAC,QAAA,GACtB/D,WAAW,iBACVhB,OAAA;QAAK8E,SAAS,EAAC,4EAA4E;QAAAC,QAAA,eACzF/E,OAAA;UAAK8E,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClD/E,OAAA,CAACF,OAAO;YAACkF,IAAI,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrBpF,OAAA;YAAG8E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAtD,KAAK,CAACuD,OAAO,CAACzE,gBAAgB,CAAC,IAAIA,gBAAgB,CAAC0E,MAAM,GAAG,CAAC,gBAC7DtF,OAAA,CAACP,aAAa;QACZ8C,IAAI,EAAE7B,WAAY;QAClBmB,SAAS,EAAEjB,gBAAiB;QAC5B2E,QAAQ,EAAEnB,gBAAiB;QAC3BoB,MAAM,EAAEX;MAAsB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,gBAEFpF,OAAA;QAAK8E,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvE/E,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/E,OAAA;YAAK8E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCpF,OAAA;YAAI8E,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFpF,OAAA;YAAG8E,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/EpF,OAAA;YACEyF,OAAO,EAAEZ,qBAAsB;YAC/BC,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAC5F;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV;;EAEA;EACA,oBACEpF,OAAA;IAAK8E,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtC/E,OAAA;MAAK8E,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpE/E,OAAA;QAAK8E,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D/E,OAAA,CAAChB,MAAM,CAAC0G,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEnE,QAAQ,EAAE;UAAI,CAAE;UAC9BkD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAEvB/E,OAAA;YAAK8E,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD/E,OAAA,CAACd,OAAO;cAAC4F,SAAS,EAAC;YAA8B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDpF,OAAA;cAAI8E,SAAS,EAAC,gCAAgC;cAAAC,QAAA,GAAC,OACxC,eAAA/E,OAAA;gBAAM8E,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNpF,OAAA;YAAG8E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEtD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJpF,OAAA;YAAK8E,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnE/E,OAAA;cAAK8E,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC/E,OAAA,CAACb,QAAQ;gBAAC2F,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChCpF,OAAA;gBAAA+E,QAAA,EAAM;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACNpF,OAAA;cAAK8E,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC/E,OAAA,CAACX,MAAM;gBAACyF,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9BpF,OAAA;gBAAA+E,QAAA,EAAM;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACNpF,OAAA;cAAK8E,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC/E,OAAA,CAACV,MAAM;gBAACwF,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9BpF,OAAA;gBAAA+E,QAAA,EAAM;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpF,OAAA,CAACR,aAAa;MACZc,OAAO,EAAEA,OAAQ;MACjBE,WAAW,EAAEA,WAAY;MACzBwF,WAAW,EAAE9C,eAAgB;MAC7BpC,OAAO,EAAEA;IAAQ;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGFpF,OAAA;MAAK8E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC/E,OAAA;QAAK8E,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1D/E,OAAA;UAAK8E,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChE/E,OAAA,CAAChB,MAAM,CAAC0G,GAAG;YACTO,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BpB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBAErC/E,OAAA,CAACd,OAAO;cAAC4F,SAAS,EAAC;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DpF,OAAA;cAAI8E,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EpF,OAAA;cAAG8E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAkD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAEbpF,OAAA,CAAChB,MAAM,CAAC0G,GAAG;YACTO,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BpB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBAEtC/E,OAAA,CAACb,QAAQ;cAAC2F,SAAS,EAAC;YAAuC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DpF,OAAA;cAAI8E,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EpF,OAAA;cAAG8E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAgD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEbpF,OAAA,CAAChB,MAAM,CAAC0G,GAAG;YACTO,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BpB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAEvC/E,OAAA,CAACX,MAAM;cAACyF,SAAS,EAAC;YAAwC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DpF,OAAA;cAAI8E,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EpF,OAAA;cAAG8E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAyC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClF,EAAA,CAteID,cAAc;EAAA,QACDlB,WAAW;AAAA;AAAAoH,EAAA,GADxBlG,cAAc;AAwepB,eAAeA,cAAc;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}