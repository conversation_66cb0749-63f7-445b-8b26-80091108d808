import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Tb<PERSON>lock,
  TbQuestionMark,
  TbPlayerPlay,
  TbStar,
  TbTarget,
  TbTrophy,
  TbBrain,
  TbCheck,
  TbX,
  TbEye,
  TbPhoto,
  TbEdit,
} from 'react-icons/tb';

const QuizCard = ({
  quiz,
  onStart,
  onView,
  showResults = false,
  userResult = null,
  className = '',
  ...props
}) => {
  const [showPreview, setShowPreview] = useState(false);

  // Debug: Check if quiz.questions contains objects
  if (quiz?.questions && Array.isArray(quiz.questions)) {
    console.log('QuizCard - quiz.questions type check:', typeof quiz.questions[0]);
    if (typeof quiz.questions[0] === 'object') {
      console.warn('QuizCard - Found question objects in quiz.questions array!');
    }
  }
  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'bg-green-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-white';
      case 'hard':
        return 'bg-red-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // Get sample questions for preview
  const getSampleQuestions = () => {
    if (!quiz?.questions || !Array.isArray(quiz.questions)) return [];

    // Get first 2-3 questions for preview
    return quiz.questions.slice(0, 3).map(question => {
      // Handle both object and string formats
      if (typeof question === 'object') {
        return question;
      }
      // If it's a string (question ID), we can't preview it
      return null;
    }).filter(Boolean);
  };

  const getQuizStatus = () => {
    if (!userResult) {
      return {
        status: 'not-attempted',
        statusColor: 'bg-blue-500',
        borderColor: 'border-blue-200',
        cardBg: 'bg-white',
        textColor: 'text-gray-800',
      };
    }

    const passingMarks = quiz.passingMarks || 60;
    const passed = userResult.percentage >= passingMarks;

    if (passed) {
      return {
        status: 'passed',
        statusColor: 'bg-green-500',
        borderColor: 'border-green-200',
        cardBg: 'bg-green-50',
        textColor: 'text-gray-800',
      };
    } else {
      return {
        status: 'failed',
        statusColor: 'bg-red-500',
        borderColor: 'border-red-200',
        cardBg: 'bg-red-50',
        textColor: 'text-gray-800',
      };
    }
  };

  const quizStatus = getQuizStatus();
  const sampleQuestions = getSampleQuestions();

  // Render question preview
  const renderQuestionPreview = (question, index) => {
    const questionType = question.type || question.answerType;

    return (
      <div key={index} className="mb-3 p-3 bg-gray-50 rounded-lg border">
        <div className="text-sm font-medium text-gray-700 mb-2">
          Q{index + 1}: {question.name?.substring(0, 60)}...
        </div>

        {/* Show image if available */}
        {question.image && (
          <div className="mb-2">
            <div className="flex items-center gap-2 mb-2">
              <TbPhoto className="w-3 h-3 text-gray-500" />
              <span className="text-xs text-gray-500">Question Image:</span>
            </div>
            <div className="relative">
              <img
                src={question.image}
                alt="Question preview"
                className="w-full h-20 object-cover rounded border border-gray-200"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'block';
                }}
                onLoad={(e) => {
                  e.target.nextSibling.style.display = 'none';
                }}
              />
              <div
                className="w-full h-20 bg-gray-100 rounded border border-gray-200 flex items-center justify-center text-xs text-gray-500"
                style={{ display: 'none' }}
              >
                <div className="text-center">
                  <TbPhoto className="w-4 h-4 mx-auto mb-1" />
                  <div>Image unavailable</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Show question type specific preview */}
        {(questionType === 'mcq' || questionType === 'Options' || question.options) && (
          <div className="space-y-1">
            {Object.entries(question.options || {}).slice(0, 2).map(([key, value]) => (
              <div key={key} className="text-xs text-gray-600 flex items-center gap-2">
                <span className="w-4 h-4 border border-gray-300 rounded text-center text-xs">{key}</span>
                <span>{String(value).substring(0, 30)}...</span>
              </div>
            ))}
            {Object.keys(question.options || {}).length > 2 && (
              <div className="text-xs text-gray-400">...and more options</div>
            )}
          </div>
        )}

        {/* Fill in the blank preview */}
        {(questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text') && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs text-gray-500 mb-2">
              <TbEdit className="w-3 h-3" />
              <span>Fill in the blank question</span>
            </div>
            <div className="relative">
              <input
                type="text"
                placeholder="Type your answer here..."
                className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                disabled
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <TbEdit className="w-3 h-3 text-gray-400" />
              </div>
            </div>
            {question.correctAnswer && (
              <div className="text-xs text-gray-400 italic">
                Expected answer: {String(question.correctAnswer).substring(0, 20)}...
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className={`h-full ${className}`}
    >
      <div
        className={`h-full border-2 shadow-lg hover:shadow-xl transition-all duration-300 relative rounded-xl ${quizStatus.cardBg} ${quizStatus.borderColor} ${quizStatus.textColor} overflow-hidden`}
        {...props}
      >
        <div className="absolute top-3 right-3 z-10">
          {userResult ? (
            <div className="flex flex-col gap-1">
              <div className={`px-2 py-1 rounded-md text-xs font-bold text-white ${quizStatus.statusColor}`}>
                {quizStatus.status === 'passed' ? (
                  <>
                    <TbCheck className="w-3 h-3 inline mr-1" />
                    PASSED
                  </>
                ) : (
                  <>
                    <TbX className="w-3 h-3 inline mr-1" />
                    FAILED
                  </>
                )}
              </div>
              <div className="px-2 py-1 rounded-md text-xs font-medium bg-white text-gray-700 text-center shadow-sm">
                {userResult.percentage}% • {userResult.xpEarned || 0} XP
              </div>
            </div>
          ) : (
            <div className="px-2 py-1 rounded-md text-xs font-bold bg-gray-500 text-white">
              <TbClock className="w-3 h-3 inline mr-1" />
              NOT ATTEMPTED
            </div>
          )}
        </div>

        <div className="bg-blue-600 p-4 text-white">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
              <TbBrain className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-bold line-clamp-2 leading-tight">{quiz.name}</h3>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs bg-blue-500 px-2 py-1 rounded">Class {quiz.class || 'N/A'}</span>
                {quiz.subject && (
                  <span className="text-xs bg-blue-500 px-2 py-1 rounded">{quiz.subject}</span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3 text-xs">
            <span className="flex items-center gap-1">
              <TbQuestionMark className="w-3 h-3" />
              {quiz.questions?.length || 0}
            </span>
            <span className="flex items-center gap-1">
              <TbClock className="w-3 h-3" />
              {quiz.duration || 30}m
            </span>
            <span className="flex items-center gap-1">
              <TbTarget className="w-3 h-3" />
              {quiz.passingMarks || 60}%
            </span>
            <span className="flex items-center gap-1">
              <TbStar className="w-3 h-3" />
              {quiz.xpPoints || 100} XP
            </span>
          </div>
        </div>

        <div className="p-4 flex-1 flex flex-col">
          <p className="text-sm text-gray-600 mb-4 line-clamp-2">
            {quiz.description || 'Test your knowledge with this comprehensive quiz and track your progress'}
          </p>
          <div className="flex flex-wrap gap-2 mb-4">
            {quiz.topic && (
              <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">{quiz.topic}</span>
            )}
            {quiz.difficulty && (
              <span className={`text-xs px-2 py-1 rounded ${getDifficultyColor(quiz.difficulty)}`}>
                {quiz.difficulty}
              </span>
            )}
            {quiz.category && (
              <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">{quiz.category}</span>
            )}
          </div>

          {/* Question Preview Section */}
          {sampleQuestions.length > 0 && (
            <div className="mb-4">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowPreview(!showPreview);
                }}
                className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 font-medium mb-2"
              >
                <TbEye className="w-4 h-4" />
                {showPreview ? 'Hide Preview' : 'Preview Questions'}
              </button>

              {showPreview && (
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {sampleQuestions.map((question, index) => renderQuestionPreview(question, index))}
                  {quiz.questions?.length > sampleQuestions.length && (
                    <div className="text-xs text-gray-400 text-center py-2">
                      ...and {quiz.questions.length - sampleQuestions.length} more questions
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {userResult && (
            <div className={`border rounded-lg p-3 mb-4 ${
              quizStatus.status === 'passed'
                ? 'bg-green-50 border-green-200'
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Last Attempt</span>
                <span className={`text-lg font-bold ${
                  quizStatus.status === 'passed' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {userResult.percentage}%
                </span>
              </div>
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span>{userResult.correctAnswers || 0} correct</span>
                <span>{userResult.xpEarned || 0} XP earned</span>
              </div>
            </div>
          )}

          <div className="mt-auto pt-4 border-t border-gray-100">
            <div className="flex gap-2">
              <button
                onClick={() => onStart && quiz?._id && onStart(quiz)}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <TbPlayerPlay className="w-4 h-4" />
                {showResults && userResult ? 'Retake Quiz' : 'Start Quiz'}
              </button>

              {showResults && onView && (
                <button
                  onClick={onView}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
                >
                  <TbTrophy className="w-4 h-4" />
                  Results
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export const QuizGrid = ({ quizzes, onQuizStart, onQuizView, showResults = false, userResults = {}, className = '' }) => {
  return (
    <div className={`quiz-grid-container ${className}`}>
      {quizzes.map((quiz, index) => (
        <motion.div
          key={quiz._id || index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: Math.min(index * 0.1, 0.8) }}
          className="h-full"
        >
          <QuizCard
            quiz={quiz}
            onStart={() => onQuizStart(quiz)}
            onView={onQuizView ? () => onQuizView(quiz) : undefined}
            showResults={showResults}
            userResult={userResults[quiz._id]}
            className="h-full"
          />
        </motion.div>
      ))}
    </div>
  );
};

export default QuizCard;