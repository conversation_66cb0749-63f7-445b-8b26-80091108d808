{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\QuizInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Tb<PERSON>lock, TbChevronLeft, TbChevronRight, TbCheck, TbX, TbFlag, TbAlertCircle } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizInterface = ({\n  quiz,\n  questions = [],\n  onSubmit,\n  onExit,\n  className = ''\n}) => {\n  _s();\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeRemaining, setTimeRemaining] = useState(((quiz === null || quiz === void 0 ? void 0 : quiz.duration) || 30) * 60); // Convert to seconds\n  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);\n  const currentQuestion = questions[currentQuestionIndex];\n  const totalQuestions = questions.length;\n  const progress = (currentQuestionIndex + 1) / totalQuestions * 100;\n\n  // Timer effect\n  useEffect(() => {\n    if (timeRemaining <= 0) {\n      handleSubmit();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeRemaining(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeRemaining]);\n\n  // Format time as MM:SS\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerChange = (questionId, answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < totalQuestions - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n  const goToQuestion = index => {\n    setCurrentQuestionIndex(index);\n  };\n\n  // Submit quiz\n  const handleSubmit = () => {\n    onSubmit && onSubmit(answers);\n  };\n\n  // Render question based on type\n  const renderQuestion = () => {\n    var _answers$currentQuest;\n    if (!currentQuestion) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n          className: \"w-12 h-12 mx-auto mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Question not available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Validate question structure\n    if (typeof currentQuestion !== 'object' || !currentQuestion._id) {\n      console.warn('Invalid question structure:', currentQuestion);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n          className: \"w-12 h-12 mx-auto mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Invalid question data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this);\n    }\n    const questionType = currentQuestion.type || currentQuestion.answerType || currentQuestion.questionType;\n    console.log('Question type detected:', questionType, 'Question:', currentQuestion);\n    switch (questionType) {\n      case 'multiple-choice':\n      case 'mcq':\n      case 'Options':\n        // Handle both object and array formats for options\n        let optionsToRender = [];\n        console.log('Processing options for MCQ:', currentQuestion.options);\n        if (Array.isArray(currentQuestion.options)) {\n          // Array format: ['option1', 'option2', ...]\n          optionsToRender = currentQuestion.options.map((option, index) => ({\n            key: String.fromCharCode(65 + index),\n            // A, B, C, D\n            value: option\n          }));\n        } else if (typeof currentQuestion.options === 'object' && currentQuestion.options !== null) {\n          // Object format: {A: 'option1', B: 'option2', ...}\n          optionsToRender = Object.entries(currentQuestion.options).map(([key, value]) => ({\n            key: key,\n            value: value\n          }));\n        }\n        console.log('Options to render:', optionsToRender);\n        if (optionsToRender.length === 0) {\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n              className: \"w-12 h-12 mx-auto mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No options available for this question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this);\n        }\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: optionsToRender.map((option, index) => {\n            const isSelected = answers[currentQuestion._id] === option.key;\n            return /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              onClick: () => handleAnswerChange(currentQuestion._id, option.key),\n              className: `\n                    w-full p-4 rounded-lg border-2 text-left transition-all duration-200 shadow-sm hover:shadow-md\n                    ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}\n                  `,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `\n                      w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                      ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-500'}\n                    `,\n                  children: option.key\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `flex-1 text-base font-medium leading-relaxed ${isSelected ? 'text-blue-900' : 'text-gray-900'}`,\n                  children: String(option.value || 'No content')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this)\n            }, option.key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this);\n      case 'fill-in-the-blank':\n      case 'text':\n      case 'Fill in the Blank':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-800 text-sm font-medium mb-2\",\n              children: \"\\uD83D\\uDCA1 Instructions: Type your answer in the text box below\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-600 text-sm\",\n              children: \"This is a free-text question. Write your complete answer.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Your Answer:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: answers[currentQuestion._id] || '',\n              onChange: e => handleAnswerChange(currentQuestion._id, e.target.value),\n              placeholder: \"Type your answer here... (e.g., for 'Chemical symbol for Gold', type 'Au')\",\n              className: \"w-full p-4 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none transition-all duration-200 text-base\",\n              rows: 4,\n              style: {\n                minHeight: '100px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-sm text-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: [((_answers$currentQuest = answers[currentQuestion._id]) === null || _answers$currentQuest === void 0 ? void 0 : _answers$currentQuest.length) || 0, \" characters\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: answers[currentQuestion._id] ? '✓ Answer provided' : '⚠ No answer yet'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this);\n      case 'image':\n      case 'picture_based':\n        // Handle both object and array formats for image questions too\n        let imageOptionsToRender = [];\n        if (Array.isArray(currentQuestion.options)) {\n          imageOptionsToRender = currentQuestion.options.map((option, index) => ({\n            key: String.fromCharCode(65 + index),\n            value: option\n          }));\n        } else if (typeof currentQuestion.options === 'object' && currentQuestion.options !== null) {\n          imageOptionsToRender = Object.entries(currentQuestion.options).map(([key, value]) => ({\n            key: key,\n            value: value\n          }));\n        }\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-blue-900 mb-2\",\n                children: \"\\uD83D\\uDCF8 Image Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-blue-700 text-sm\",\n                children: \"Look at the image below and select the correct answer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), currentQuestion.imageUrl || currentQuestion.image ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg p-4 shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: currentQuestion.imageUrl || currentQuestion.image,\n                alt: \"Question Image\",\n                className: \"max-w-full h-auto rounded-lg mx-auto shadow-md max-h-64 object-contain\",\n                onLoad: e => {\n                  console.log('Image loaded successfully:', e.target.src);\n                },\n                onError: e => {\n                  console.error('Image failed to load:', e.target.src);\n                  e.target.style.display = 'none';\n                  const fallback = e.target.parentNode.querySelector('.image-fallback');\n                  if (fallback) fallback.style.display = 'block';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-fallback text-center text-gray-500 py-8 hidden\",\n                children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n                  className: \"w-12 h-12 mx-auto mb-2 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600\",\n                  children: \"Image could not be loaded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mt-1\",\n                  children: \"Please answer based on the question text\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg p-8 text-center text-gray-500 border-2 border-dashed border-gray-300\",\n              children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n                className: \"w-12 h-12 mx-auto mb-2 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600\",\n                children: \"No image provided for this question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), imageOptionsToRender.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n              className: \"w-12 h-12 mx-auto mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No options available for this image question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: imageOptionsToRender.map(option => {\n              const isSelected = answers[currentQuestion._id] === option.key;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleAnswerChange(currentQuestion._id, option.key),\n                className: `\n                        w-full p-4 rounded-lg border-2 text-left transition-all duration-200\n                        ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 bg-white hover:border-gray-300'}\n                      `,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `\n                          w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                          ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-500'}\n                        `,\n                    children: option.key\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-base font-medium ${isSelected ? 'text-blue-900' : 'text-gray-800'}`,\n                    children: option.value || 'No content'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 23\n                }, this)\n              }, option.key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8 text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n            className: \"w-12 h-12 mx-auto mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Unsupported question type: \", currentQuestion.type || currentQuestion.answerType]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen bg-gray-50 flex flex-col ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b sticky top-0 z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: (quiz === null || quiz === void 0 ? void 0 : quiz.name) || 'Quiz'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: (quiz === null || quiz === void 0 ? void 0 : quiz.subject) || 'Subject'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `\n              flex items-center gap-2 px-4 py-2 rounded-lg font-mono text-lg font-bold\n              ${timeRemaining <= 300 ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'}\n            `,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), formatTime(timeRemaining)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between text-sm text-gray-600 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Question \", currentQuestionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round(progress), \"% Complete\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-blue-500 h-2 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progress}%`\n              },\n              transition: {\n                duration: 0.3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 max-w-4xl mx-auto w-full px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-xl shadow-sm border p-6 lg:p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n              children: (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.name) || (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.question) || 'Question not available'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), (currentQuestion === null || currentQuestion === void 0 ? void 0 : currentQuestion.description) && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4\",\n              children: currentQuestion.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), renderQuestion()]\n        }, currentQuestionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t sticky bottom-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestionIndex === 0,\n            className: `\n                flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors\n                ${currentQuestionIndex === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-100'}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(TbChevronLeft, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 overflow-x-auto max-w-md\",\n            children: questions && Array.isArray(questions) ? questions.map((question, index) => {\n              // Safety check for question object\n              if (!question || typeof question !== 'object') {\n                console.warn('Invalid question at index:', index, question);\n                return null;\n              }\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => goToQuestion(index),\n                className: `\n                      w-8 h-8 rounded-full text-sm font-medium transition-colors flex-shrink-0\n                      ${index === currentQuestionIndex ? 'bg-blue-500 text-white' : answers[question._id] ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}\n                    `,\n                children: index + 1\n              }, question._id || index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this);\n            }) : null\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), currentQuestionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowSubmitConfirm(true),\n            className: \"flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this), \"Submit Quiz\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToNext,\n            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors\",\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(TbChevronRight, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: showSubmitConfirm && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0.9,\n            opacity: 0\n          },\n          animate: {\n            scale: 1,\n            opacity: 1\n          },\n          exit: {\n            scale: 0.9,\n            opacity: 0\n          },\n          className: \"bg-white rounded-xl p-6 max-w-md w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold text-gray-900 mb-4\",\n            children: \"Submit Quiz?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Are you sure you want to submit your quiz? You won't be able to change your answers after submission.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSubmitConfirm(false),\n              className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmit,\n              className: \"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n              children: \"Submit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 327,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizInterface, \"xoS+oLqbqtVr+EDICtQ8Jyf/8MM=\");\n_c = QuizInterface;\nexport default QuizInterface;\nvar _c;\n$RefreshReg$(_c, \"QuizInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "AnimatePresence", "TbClock", "TbChevronLeft", "TbChevronRight", "TbCheck", "TbX", "TbFlag", "TbAlertCircle", "jsxDEV", "_jsxDEV", "QuizInterface", "quiz", "questions", "onSubmit", "onExit", "className", "_s", "currentQuestionIndex", "setCurrentQuestionIndex", "answers", "setAnswers", "timeRemaining", "setTimeRemaining", "duration", "showSubmitConfirm", "setShowSubmitConfirm", "currentQuestion", "totalQuestions", "length", "progress", "handleSubmit", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "handleAnswerChange", "questionId", "answer", "goToNext", "goToPrevious", "goToQuestion", "index", "renderQuestion", "_answers$currentQuest", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_id", "console", "warn", "questionType", "type", "answerType", "log", "optionsToRender", "options", "Array", "isArray", "map", "option", "key", "String", "fromCharCode", "value", "Object", "entries", "isSelected", "button", "whileHover", "scale", "whileTap", "onClick", "onChange", "e", "target", "placeholder", "rows", "style", "minHeight", "imageOptionsToRender", "imageUrl", "image", "src", "alt", "onLoad", "onError", "error", "display", "fallback", "parentNode", "querySelector", "name", "subject", "round", "div", "initial", "width", "animate", "transition", "mode", "opacity", "x", "exit", "question", "description", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/QuizInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Tb<PERSON>lock,\n  TbChevronLeft,\n  TbChevronRight,\n  TbCheck,\n  TbX,\n  TbFlag,\n  TbAlertCircle,\n} from 'react-icons/tb';\n\nconst QuizInterface = ({\n  quiz,\n  questions = [],\n  onSubmit,\n  onExit,\n  className = ''\n}) => {\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeRemaining, setTimeRemaining] = useState((quiz?.duration || 30) * 60); // Convert to seconds\n  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const totalQuestions = questions.length;\n  const progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;\n\n  // Timer effect\n  useEffect(() => {\n    if (timeRemaining <= 0) {\n      handleSubmit();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeRemaining(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeRemaining]);\n\n  // Format time as MM:SS\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerChange = (questionId, answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < totalQuestions - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  const goToQuestion = (index) => {\n    setCurrentQuestionIndex(index);\n  };\n\n  // Submit quiz\n  const handleSubmit = () => {\n    onSubmit && onSubmit(answers);\n  };\n\n  // Render question based on type\n  const renderQuestion = () => {\n    if (!currentQuestion) {\n      return (\n        <div className=\"text-center py-8 text-gray-500\">\n          <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n          <p>Question not available</p>\n        </div>\n      );\n    }\n\n    // Validate question structure\n    if (typeof currentQuestion !== 'object' || !currentQuestion._id) {\n      console.warn('Invalid question structure:', currentQuestion);\n      return (\n        <div className=\"text-center py-8 text-gray-500\">\n          <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n          <p>Invalid question data</p>\n        </div>\n      );\n    }\n\n    const questionType = currentQuestion.type || currentQuestion.answerType || currentQuestion.questionType;\n    console.log('Question type detected:', questionType, 'Question:', currentQuestion);\n\n    switch (questionType) {\n      case 'multiple-choice':\n      case 'mcq':\n      case 'Options':\n        // Handle both object and array formats for options\n        let optionsToRender = [];\n\n        console.log('Processing options for MCQ:', currentQuestion.options);\n\n        if (Array.isArray(currentQuestion.options)) {\n          // Array format: ['option1', 'option2', ...]\n          optionsToRender = currentQuestion.options.map((option, index) => ({\n            key: String.fromCharCode(65 + index), // A, B, C, D\n            value: option\n          }));\n        } else if (typeof currentQuestion.options === 'object' && currentQuestion.options !== null) {\n          // Object format: {A: 'option1', B: 'option2', ...}\n          optionsToRender = Object.entries(currentQuestion.options).map(([key, value]) => ({\n            key: key,\n            value: value\n          }));\n        }\n\n        console.log('Options to render:', optionsToRender);\n\n        if (optionsToRender.length === 0) {\n          return (\n            <div className=\"text-center py-8 text-gray-500\">\n              <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n              <p>No options available for this question</p>\n            </div>\n          );\n        }\n\n        return (\n          <div className=\"space-y-3\">\n            {optionsToRender.map((option, index) => {\n              const isSelected = answers[currentQuestion._id] === option.key;\n\n              return (\n                <motion.button\n                  key={option.key}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  onClick={() => handleAnswerChange(currentQuestion._id, option.key)}\n                  className={`\n                    w-full p-4 rounded-lg border-2 text-left transition-all duration-200 shadow-sm hover:shadow-md\n                    ${isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                    }\n                  `}\n                >\n                  <div className=\"flex items-center gap-3\">\n                    <div className={`\n                      w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                      ${isSelected\n                        ? 'border-blue-500 bg-blue-500 text-white'\n                        : 'border-gray-300 text-gray-500'\n                      }\n                    `}>\n                      {option.key}\n                    </div>\n                    <span className={`flex-1 text-base font-medium leading-relaxed ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>\n                      {String(option.value || 'No content')}\n                    </span>\n                  </div>\n                </motion.button>\n              );\n            })}\n          </div>\n        );\n\n      case 'fill-in-the-blank':\n      case 'text':\n      case 'Fill in the Blank':\n        return (\n          <div className=\"space-y-4\">\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <p className=\"text-blue-800 text-sm font-medium mb-2\">\n                💡 Instructions: Type your answer in the text box below\n              </p>\n              <p className=\"text-blue-600 text-sm\">\n                This is a free-text question. Write your complete answer.\n              </p>\n            </div>\n\n            <div className=\"space-y-2\">\n              <label className=\"block text-sm font-medium text-gray-700\">\n                Your Answer:\n              </label>\n              <textarea\n                value={answers[currentQuestion._id] || ''}\n                onChange={(e) => handleAnswerChange(currentQuestion._id, e.target.value)}\n                placeholder=\"Type your answer here... (e.g., for 'Chemical symbol for Gold', type 'Au')\"\n                className=\"w-full p-4 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none transition-all duration-200 text-base\"\n                rows={4}\n                style={{ minHeight: '100px' }}\n              />\n              <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                <span>\n                  {answers[currentQuestion._id]?.length || 0} characters\n                </span>\n                <span>\n                  {answers[currentQuestion._id] ? '✓ Answer provided' : '⚠ No answer yet'}\n                </span>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 'image':\n      case 'picture_based':\n        // Handle both object and array formats for image questions too\n        let imageOptionsToRender = [];\n\n        if (Array.isArray(currentQuestion.options)) {\n          imageOptionsToRender = currentQuestion.options.map((option, index) => ({\n            key: String.fromCharCode(65 + index),\n            value: option\n          }));\n        } else if (typeof currentQuestion.options === 'object' && currentQuestion.options !== null) {\n          imageOptionsToRender = Object.entries(currentQuestion.options).map(([key, value]) => ({\n            key: key,\n            value: value\n          }));\n        }\n\n        return (\n          <div className=\"space-y-4\">\n            {/* Image Display Section */}\n            <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\">\n              <div className=\"text-center mb-4\">\n                <h4 className=\"text-lg font-semibold text-blue-900 mb-2\">📸 Image Question</h4>\n                <p className=\"text-blue-700 text-sm\">Look at the image below and select the correct answer</p>\n              </div>\n\n              {(currentQuestion.imageUrl || currentQuestion.image) ? (\n                <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n                  <img\n                    src={currentQuestion.imageUrl || currentQuestion.image}\n                    alt=\"Question Image\"\n                    className=\"max-w-full h-auto rounded-lg mx-auto shadow-md max-h-64 object-contain\"\n                    onLoad={(e) => {\n                      console.log('Image loaded successfully:', e.target.src);\n                    }}\n                    onError={(e) => {\n                      console.error('Image failed to load:', e.target.src);\n                      e.target.style.display = 'none';\n                      const fallback = e.target.parentNode.querySelector('.image-fallback');\n                      if (fallback) fallback.style.display = 'block';\n                    }}\n                  />\n                  <div className=\"image-fallback text-center text-gray-500 py-8 hidden\">\n                    <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2 text-gray-400\" />\n                    <p className=\"text-gray-600\">Image could not be loaded</p>\n                    <p className=\"text-sm text-gray-500 mt-1\">Please answer based on the question text</p>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"bg-white rounded-lg p-8 text-center text-gray-500 border-2 border-dashed border-gray-300\">\n                  <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2 text-gray-400\" />\n                  <p className=\"text-gray-600\">No image provided for this question</p>\n                </div>\n              )}\n            </div>\n\n            {imageOptionsToRender.length === 0 ? (\n              <div className=\"text-center py-8 text-gray-500\">\n                <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n                <p>No options available for this image question</p>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {imageOptionsToRender.map((option) => {\n                  const isSelected = answers[currentQuestion._id] === option.key;\n\n                  return (\n                    <button\n                      key={option.key}\n                      onClick={() => handleAnswerChange(currentQuestion._id, option.key)}\n                      className={`\n                        w-full p-4 rounded-lg border-2 text-left transition-all duration-200\n                        ${isSelected\n                          ? 'border-blue-500 bg-blue-50 text-blue-900'\n                          : 'border-gray-200 bg-white hover:border-gray-300'\n                        }\n                      `}\n                    >\n                      <div className=\"flex items-center gap-3\">\n                        <div className={`\n                          w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                          ${isSelected\n                            ? 'border-blue-500 bg-blue-500 text-white'\n                            : 'border-gray-300 text-gray-500'\n                          }\n                        `}>\n                          {option.key}\n                        </div>\n                        <span className={`text-base font-medium ${isSelected ? 'text-blue-900' : 'text-gray-800'}`}>\n                          {option.value || 'No content'}\n                        </span>\n                      </div>\n                    </button>\n                  );\n                })}\n              </div>\n            )}\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"text-center py-8 text-gray-500\">\n            <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n            <p>Unsupported question type: {currentQuestion.type || currentQuestion.answerType}</p>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 flex flex-col ${className}`}>\n      {/* Top Bar */}\n      <div className=\"bg-white shadow-sm border-b sticky top-0 z-10\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Title */}\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900\">{quiz?.name || 'Quiz'}</h1>\n              <p className=\"text-sm text-gray-600\">{quiz?.subject || 'Subject'}</p>\n            </div>\n\n            {/* Timer */}\n            <div className={`\n              flex items-center gap-2 px-4 py-2 rounded-lg font-mono text-lg font-bold\n              ${timeRemaining <= 300 ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'}\n            `}>\n              <TbClock className=\"w-5 h-5\" />\n              {formatTime(timeRemaining)}\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4\">\n            <div className=\"flex items-center justify-between text-sm text-gray-600 mb-2\">\n              <span>Question {currentQuestionIndex + 1} of {totalQuestions}</span>\n              <span>{Math.round(progress)}% Complete</span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-blue-500 h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progress}%` }}\n                transition={{ duration: 0.3 }}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Question Content */}\n      <div className=\"flex-1 max-w-4xl mx-auto w-full px-4 py-8\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentQuestionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-xl shadow-sm border p-6 lg:p-8\"\n          >\n            {/* Question */}\n            <div className=\"mb-8\">\n              <h2 className=\"text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                {currentQuestion?.name || currentQuestion?.question || 'Question not available'}\n              </h2>\n              \n              {currentQuestion?.description && (\n                <p className=\"text-gray-600 mb-4\">{currentQuestion.description}</p>\n              )}\n            </div>\n\n            {/* Answer Options */}\n            {renderQuestion()}\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Bottom Navigation */}\n      <div className=\"bg-white border-t sticky bottom-0\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Previous Button */}\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestionIndex === 0}\n              className={`\n                flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors\n                ${currentQuestionIndex === 0\n                  ? 'text-gray-400 cursor-not-allowed'\n                  : 'text-gray-700 hover:bg-gray-100'\n                }\n              `}\n            >\n              <TbChevronLeft className=\"w-5 h-5\" />\n              Previous\n            </button>\n\n            {/* Question Numbers */}\n            <div className=\"flex items-center gap-2 overflow-x-auto max-w-md\">\n              {questions && Array.isArray(questions) ? questions.map((question, index) => {\n                // Safety check for question object\n                if (!question || typeof question !== 'object') {\n                  console.warn('Invalid question at index:', index, question);\n                  return null;\n                }\n\n                return (\n                  <button\n                    key={question._id || index}\n                    onClick={() => goToQuestion(index)}\n                    className={`\n                      w-8 h-8 rounded-full text-sm font-medium transition-colors flex-shrink-0\n                      ${index === currentQuestionIndex\n                        ? 'bg-blue-500 text-white'\n                        : answers[question._id]\n                        ? 'bg-green-100 text-green-600'\n                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                      }\n                    `}\n                  >\n                    {index + 1}\n                  </button>\n                );\n              }) : null}\n            </div>\n\n            {/* Next/Submit Button */}\n            {currentQuestionIndex === totalQuestions - 1 ? (\n              <button\n                onClick={() => setShowSubmitConfirm(true)}\n                className=\"flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors\"\n              >\n                <TbCheck className=\"w-5 h-5\" />\n                Submit Quiz\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Next\n                <TbChevronRight className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Submit Confirmation Modal */}\n      <AnimatePresence>\n        {showSubmitConfirm && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\"\n          >\n            <motion.div\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0.9, opacity: 0 }}\n              className=\"bg-white rounded-xl p-6 max-w-md w-full\"\n            >\n              <h3 className=\"text-lg font-bold text-gray-900 mb-4\">Submit Quiz?</h3>\n              <p className=\"text-gray-600 mb-6\">\n                Are you sure you want to submit your quiz? You won't be able to change your answers after submission.\n              </p>\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={() => setShowSubmitConfirm(false)}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSubmit}\n                  className=\"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  Submit\n                </button>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default QuizInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,aAAa,QACR,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,aAAa,GAAGA,CAAC;EACrBC,IAAI;EACJC,SAAS,GAAG,EAAE;EACdC,QAAQ;EACRC,MAAM;EACNC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAAe,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,QAAQ,KAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACjF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM8B,eAAe,GAAGd,SAAS,CAACK,oBAAoB,CAAC;EACvD,MAAMU,cAAc,GAAGf,SAAS,CAACgB,MAAM;EACvC,MAAMC,QAAQ,GAAI,CAACZ,oBAAoB,GAAG,CAAC,IAAIU,cAAc,GAAI,GAAG;;EAEpE;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIwB,aAAa,IAAI,CAAC,EAAE;MACtBS,YAAY,CAAC,CAAC;MACd;IACF;IAEA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BV,gBAAgB,CAACW,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACV,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMc,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAQ,GAAEC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,IAAGF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EAClF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,MAAM,KAAK;IACjDzB,UAAU,CAACa,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACW,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI7B,oBAAoB,GAAGU,cAAc,GAAG,CAAC,EAAE;MAC7CT,uBAAuB,CAACe,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI9B,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACe,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMe,YAAY,GAAIC,KAAK,IAAK;IAC9B/B,uBAAuB,CAAC+B,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMnB,YAAY,GAAGA,CAAA,KAAM;IACzBjB,QAAQ,IAAIA,QAAQ,CAACM,OAAO,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM+B,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,IAAI,CAACzB,eAAe,EAAE;MACpB,oBACEjB,OAAA;QAAKM,SAAS,EAAC,gCAAgC;QAAAqC,QAAA,gBAC7C3C,OAAA,CAACF,aAAa;UAACQ,SAAS,EAAC;QAAwB;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpD/C,OAAA;UAAA2C,QAAA,EAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAEV;;IAEA;IACA,IAAI,OAAO9B,eAAe,KAAK,QAAQ,IAAI,CAACA,eAAe,CAAC+B,GAAG,EAAE;MAC/DC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEjC,eAAe,CAAC;MAC5D,oBACEjB,OAAA;QAAKM,SAAS,EAAC,gCAAgC;QAAAqC,QAAA,gBAC7C3C,OAAA,CAACF,aAAa;UAACQ,SAAS,EAAC;QAAwB;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpD/C,OAAA;UAAA2C,QAAA,EAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAEV;IAEA,MAAMI,YAAY,GAAGlC,eAAe,CAACmC,IAAI,IAAInC,eAAe,CAACoC,UAAU,IAAIpC,eAAe,CAACkC,YAAY;IACvGF,OAAO,CAACK,GAAG,CAAC,yBAAyB,EAAEH,YAAY,EAAE,WAAW,EAAElC,eAAe,CAAC;IAElF,QAAQkC,YAAY;MAClB,KAAK,iBAAiB;MACtB,KAAK,KAAK;MACV,KAAK,SAAS;QACZ;QACA,IAAII,eAAe,GAAG,EAAE;QAExBN,OAAO,CAACK,GAAG,CAAC,6BAA6B,EAAErC,eAAe,CAACuC,OAAO,CAAC;QAEnE,IAAIC,KAAK,CAACC,OAAO,CAACzC,eAAe,CAACuC,OAAO,CAAC,EAAE;UAC1C;UACAD,eAAe,GAAGtC,eAAe,CAACuC,OAAO,CAACG,GAAG,CAAC,CAACC,MAAM,EAAEpB,KAAK,MAAM;YAChEqB,GAAG,EAAEC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGvB,KAAK,CAAC;YAAE;YACtCwB,KAAK,EAAEJ;UACT,CAAC,CAAC,CAAC;QACL,CAAC,MAAM,IAAI,OAAO3C,eAAe,CAACuC,OAAO,KAAK,QAAQ,IAAIvC,eAAe,CAACuC,OAAO,KAAK,IAAI,EAAE;UAC1F;UACAD,eAAe,GAAGU,MAAM,CAACC,OAAO,CAACjD,eAAe,CAACuC,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,CAACE,GAAG,EAAEG,KAAK,CAAC,MAAM;YAC/EH,GAAG,EAAEA,GAAG;YACRG,KAAK,EAAEA;UACT,CAAC,CAAC,CAAC;QACL;QAEAf,OAAO,CAACK,GAAG,CAAC,oBAAoB,EAAEC,eAAe,CAAC;QAElD,IAAIA,eAAe,CAACpC,MAAM,KAAK,CAAC,EAAE;UAChC,oBACEnB,OAAA;YAAKM,SAAS,EAAC,gCAAgC;YAAAqC,QAAA,gBAC7C3C,OAAA,CAACF,aAAa;cAACQ,SAAS,EAAC;YAAwB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/C,OAAA;cAAA2C,QAAA,EAAG;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAEV;QAEA,oBACE/C,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAqC,QAAA,EACvBY,eAAe,CAACI,GAAG,CAAC,CAACC,MAAM,EAAEpB,KAAK,KAAK;YACtC,MAAM2B,UAAU,GAAGzD,OAAO,CAACO,eAAe,CAAC+B,GAAG,CAAC,KAAKY,MAAM,CAACC,GAAG;YAE9D,oBACE7D,OAAA,CAACV,MAAM,CAAC8E,MAAM;cAEZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BE,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAACjB,eAAe,CAAC+B,GAAG,EAAEY,MAAM,CAACC,GAAG,CAAE;cACnEvD,SAAS,EAAG;AAC9B;AACA,sBAAsB6D,UAAU,GACR,0CAA0C,GAC1C,iEACH;AACrB,mBAAoB;cAAAxB,QAAA,eAEF3C,OAAA;gBAAKM,SAAS,EAAC,yBAAyB;gBAAAqC,QAAA,gBACtC3C,OAAA;kBAAKM,SAAS,EAAG;AACrC;AACA,wBAAwB6D,UAAU,GACR,wCAAwC,GACxC,+BACH;AACvB,qBAAsB;kBAAAxB,QAAA,EACCiB,MAAM,CAACC;gBAAG;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACN/C,OAAA;kBAAMM,SAAS,EAAG,gDAA+C6D,UAAU,GAAG,eAAe,GAAG,eAAgB,EAAE;kBAAAxB,QAAA,EAC/GmB,MAAM,CAACF,MAAM,CAACI,KAAK,IAAI,YAAY;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GAzBDa,MAAM,CAACC,GAAG;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BF,CAAC;UAEpB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAGV,KAAK,mBAAmB;MACxB,KAAK,MAAM;MACX,KAAK,mBAAmB;QACtB,oBACE/C,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAqC,QAAA,gBACxB3C,OAAA;YAAKM,SAAS,EAAC,kDAAkD;YAAAqC,QAAA,gBAC/D3C,OAAA;cAAGM,SAAS,EAAC,wCAAwC;cAAAqC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ/C,OAAA;cAAGM,SAAS,EAAC,uBAAuB;cAAAqC,QAAA,EAAC;YAErC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN/C,OAAA;YAAKM,SAAS,EAAC,WAAW;YAAAqC,QAAA,gBACxB3C,OAAA;cAAOM,SAAS,EAAC,yCAAyC;cAAAqC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/C,OAAA;cACEgE,KAAK,EAAEtD,OAAO,CAACO,eAAe,CAAC+B,GAAG,CAAC,IAAI,EAAG;cAC1CyB,QAAQ,EAAGC,CAAC,IAAKxC,kBAAkB,CAACjB,eAAe,CAAC+B,GAAG,EAAE0B,CAAC,CAACC,MAAM,CAACX,KAAK,CAAE;cACzEY,WAAW,EAAC,4EAA4E;cACxFtE,SAAS,EAAC,yJAAyJ;cACnKuE,IAAI,EAAE,CAAE;cACRC,KAAK,EAAE;gBAAEC,SAAS,EAAE;cAAQ;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACF/C,OAAA;cAAKM,SAAS,EAAC,yDAAyD;cAAAqC,QAAA,gBACtE3C,OAAA;gBAAA2C,QAAA,GACG,EAAAD,qBAAA,GAAAhC,OAAO,CAACO,eAAe,CAAC+B,GAAG,CAAC,cAAAN,qBAAA,uBAA5BA,qBAAA,CAA8BvB,MAAM,KAAI,CAAC,EAAC,aAC7C;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACP/C,OAAA;gBAAA2C,QAAA,EACGjC,OAAO,CAACO,eAAe,CAAC+B,GAAG,CAAC,GAAG,mBAAmB,GAAG;cAAiB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,OAAO;MACZ,KAAK,eAAe;QAClB;QACA,IAAIiC,oBAAoB,GAAG,EAAE;QAE7B,IAAIvB,KAAK,CAACC,OAAO,CAACzC,eAAe,CAACuC,OAAO,CAAC,EAAE;UAC1CwB,oBAAoB,GAAG/D,eAAe,CAACuC,OAAO,CAACG,GAAG,CAAC,CAACC,MAAM,EAAEpB,KAAK,MAAM;YACrEqB,GAAG,EAAEC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGvB,KAAK,CAAC;YACpCwB,KAAK,EAAEJ;UACT,CAAC,CAAC,CAAC;QACL,CAAC,MAAM,IAAI,OAAO3C,eAAe,CAACuC,OAAO,KAAK,QAAQ,IAAIvC,eAAe,CAACuC,OAAO,KAAK,IAAI,EAAE;UAC1FwB,oBAAoB,GAAGf,MAAM,CAACC,OAAO,CAACjD,eAAe,CAACuC,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,CAACE,GAAG,EAAEG,KAAK,CAAC,MAAM;YACpFH,GAAG,EAAEA,GAAG;YACRG,KAAK,EAAEA;UACT,CAAC,CAAC,CAAC;QACL;QAEA,oBACEhE,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAqC,QAAA,gBAExB3C,OAAA;YAAKM,SAAS,EAAC,mFAAmF;YAAAqC,QAAA,gBAChG3C,OAAA;cAAKM,SAAS,EAAC,kBAAkB;cAAAqC,QAAA,gBAC/B3C,OAAA;gBAAIM,SAAS,EAAC,0CAA0C;gBAAAqC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/E/C,OAAA;gBAAGM,SAAS,EAAC,uBAAuB;gBAAAqC,QAAA,EAAC;cAAqD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,EAEJ9B,eAAe,CAACgE,QAAQ,IAAIhE,eAAe,CAACiE,KAAK,gBACjDlF,OAAA;cAAKM,SAAS,EAAC,mCAAmC;cAAAqC,QAAA,gBAChD3C,OAAA;gBACEmF,GAAG,EAAElE,eAAe,CAACgE,QAAQ,IAAIhE,eAAe,CAACiE,KAAM;gBACvDE,GAAG,EAAC,gBAAgB;gBACpB9E,SAAS,EAAC,wEAAwE;gBAClF+E,MAAM,EAAGX,CAAC,IAAK;kBACbzB,OAAO,CAACK,GAAG,CAAC,4BAA4B,EAAEoB,CAAC,CAACC,MAAM,CAACQ,GAAG,CAAC;gBACzD,CAAE;gBACFG,OAAO,EAAGZ,CAAC,IAAK;kBACdzB,OAAO,CAACsC,KAAK,CAAC,uBAAuB,EAAEb,CAAC,CAACC,MAAM,CAACQ,GAAG,CAAC;kBACpDT,CAAC,CAACC,MAAM,CAACG,KAAK,CAACU,OAAO,GAAG,MAAM;kBAC/B,MAAMC,QAAQ,GAAGf,CAAC,CAACC,MAAM,CAACe,UAAU,CAACC,aAAa,CAAC,iBAAiB,CAAC;kBACrE,IAAIF,QAAQ,EAAEA,QAAQ,CAACX,KAAK,CAACU,OAAO,GAAG,OAAO;gBAChD;cAAE;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF/C,OAAA;gBAAKM,SAAS,EAAC,sDAAsD;gBAAAqC,QAAA,gBACnE3C,OAAA,CAACF,aAAa;kBAACQ,SAAS,EAAC;gBAAsC;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClE/C,OAAA;kBAAGM,SAAS,EAAC,eAAe;kBAAAqC,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC1D/C,OAAA;kBAAGM,SAAS,EAAC,4BAA4B;kBAAAqC,QAAA,EAAC;gBAAwC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN/C,OAAA;cAAKM,SAAS,EAAC,0FAA0F;cAAAqC,QAAA,gBACvG3C,OAAA,CAACF,aAAa;gBAACQ,SAAS,EAAC;cAAsC;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClE/C,OAAA;gBAAGM,SAAS,EAAC,eAAe;gBAAAqC,QAAA,EAAC;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELiC,oBAAoB,CAAC7D,MAAM,KAAK,CAAC,gBAChCnB,OAAA;YAAKM,SAAS,EAAC,gCAAgC;YAAAqC,QAAA,gBAC7C3C,OAAA,CAACF,aAAa;cAACQ,SAAS,EAAC;YAAwB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/C,OAAA;cAAA2C,QAAA,EAAG;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,gBAEN/C,OAAA;YAAKM,SAAS,EAAC,WAAW;YAAAqC,QAAA,EACvBqC,oBAAoB,CAACrB,GAAG,CAAEC,MAAM,IAAK;cACpC,MAAMO,UAAU,GAAGzD,OAAO,CAACO,eAAe,CAAC+B,GAAG,CAAC,KAAKY,MAAM,CAACC,GAAG;cAE9D,oBACE7D,OAAA;gBAEEwE,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAACjB,eAAe,CAAC+B,GAAG,EAAEY,MAAM,CAACC,GAAG,CAAE;gBACnEvD,SAAS,EAAG;AAClC;AACA,0BAA0B6D,UAAU,GACR,0CAA0C,GAC1C,gDACH;AACzB,uBAAwB;gBAAAxB,QAAA,eAEF3C,OAAA;kBAAKM,SAAS,EAAC,yBAAyB;kBAAAqC,QAAA,gBACtC3C,OAAA;oBAAKM,SAAS,EAAG;AACzC;AACA,4BAA4B6D,UAAU,GACR,wCAAwC,GACxC,+BACH;AAC3B,yBAA0B;oBAAAxB,QAAA,EACCiB,MAAM,CAACC;kBAAG;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACN/C,OAAA;oBAAMM,SAAS,EAAG,yBAAwB6D,UAAU,GAAG,eAAe,GAAG,eAAgB,EAAE;oBAAAxB,QAAA,EACxFiB,MAAM,CAACI,KAAK,IAAI;kBAAY;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC,GAvBDa,MAAM,CAACC,GAAG;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBT,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV;QACE,oBACE/C,OAAA;UAAKM,SAAS,EAAC,gCAAgC;UAAAqC,QAAA,gBAC7C3C,OAAA,CAACF,aAAa;YAACQ,SAAS,EAAC;UAAwB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpD/C,OAAA;YAAA2C,QAAA,GAAG,6BAA2B,EAAC1B,eAAe,CAACmC,IAAI,IAAInC,eAAe,CAACoC,UAAU;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC;IAEZ;EACF,CAAC;EAED,oBACE/C,OAAA;IAAKM,SAAS,EAAG,yCAAwCA,SAAU,EAAE;IAAAqC,QAAA,gBAEnE3C,OAAA;MAAKM,SAAS,EAAC,+CAA+C;MAAAqC,QAAA,eAC5D3C,OAAA;QAAKM,SAAS,EAAC,6BAA6B;QAAAqC,QAAA,gBAC1C3C,OAAA;UAAKM,SAAS,EAAC,mCAAmC;UAAAqC,QAAA,gBAEhD3C,OAAA;YAAA2C,QAAA,gBACE3C,OAAA;cAAIM,SAAS,EAAC,iCAAiC;cAAAqC,QAAA,EAAE,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,IAAI,KAAI;YAAM;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3E/C,OAAA;cAAGM,SAAS,EAAC,uBAAuB;cAAAqC,QAAA,EAAE,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2F,OAAO,KAAI;YAAS;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAGN/C,OAAA;YAAKM,SAAS,EAAG;AAC7B;AACA,gBAAgBM,aAAa,IAAI,GAAG,GAAG,yBAAyB,GAAG,2BAA4B;AAC/F,aAAc;YAAA+B,QAAA,gBACA3C,OAAA,CAACR,OAAO;cAACc,SAAS,EAAC;YAAS;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC9BrB,UAAU,CAACd,aAAa,CAAC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/C,OAAA;UAAKM,SAAS,EAAC,MAAM;UAAAqC,QAAA,gBACnB3C,OAAA;YAAKM,SAAS,EAAC,8DAA8D;YAAAqC,QAAA,gBAC3E3C,OAAA;cAAA2C,QAAA,GAAM,WAAS,EAACnC,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACU,cAAc;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpE/C,OAAA;cAAA2C,QAAA,GAAOd,IAAI,CAACiE,KAAK,CAAC1E,QAAQ,CAAC,EAAC,YAAU;YAAA;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN/C,OAAA;YAAKM,SAAS,EAAC,qCAAqC;YAAAqC,QAAA,eAClD3C,OAAA,CAACV,MAAM,CAACyG,GAAG;cACTzF,SAAS,EAAC,8BAA8B;cACxC0F,OAAO,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAG,GAAE7E,QAAS;cAAG,CAAE;cACnC+E,UAAU,EAAE;gBAAErF,QAAQ,EAAE;cAAI;YAAE;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA;MAAKM,SAAS,EAAC,2CAA2C;MAAAqC,QAAA,eACxD3C,OAAA,CAACT,eAAe;QAAC6G,IAAI,EAAC,MAAM;QAAAzD,QAAA,eAC1B3C,OAAA,CAACV,MAAM,CAACyG,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BH,UAAU,EAAE;YAAErF,QAAQ,EAAE;UAAI,CAAE;UAC9BR,SAAS,EAAC,iDAAiD;UAAAqC,QAAA,gBAG3D3C,OAAA;YAAKM,SAAS,EAAC,MAAM;YAAAqC,QAAA,gBACnB3C,OAAA;cAAIM,SAAS,EAAC,sEAAsE;cAAAqC,QAAA,EACjF,CAAA1B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2E,IAAI,MAAI3E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuF,QAAQ,KAAI;YAAwB;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,EAEJ,CAAA9B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwF,WAAW,kBAC3BzG,OAAA;cAAGM,SAAS,EAAC,oBAAoB;cAAAqC,QAAA,EAAE1B,eAAe,CAACwF;YAAW;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACnE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLN,cAAc,CAAC,CAAC;QAAA,GAnBZjC,oBAAoB;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAGN/C,OAAA;MAAKM,SAAS,EAAC,mCAAmC;MAAAqC,QAAA,eAChD3C,OAAA;QAAKM,SAAS,EAAC,6BAA6B;QAAAqC,QAAA,eAC1C3C,OAAA;UAAKM,SAAS,EAAC,mCAAmC;UAAAqC,QAAA,gBAEhD3C,OAAA;YACEwE,OAAO,EAAElC,YAAa;YACtBoE,QAAQ,EAAElG,oBAAoB,KAAK,CAAE;YACrCF,SAAS,EAAG;AAC1B;AACA,kBAAkBE,oBAAoB,KAAK,CAAC,GACxB,kCAAkC,GAClC,iCACH;AACjB,eAAgB;YAAAmC,QAAA,gBAEF3C,OAAA,CAACP,aAAa;cAACa,SAAS,EAAC;YAAS;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT/C,OAAA;YAAKM,SAAS,EAAC,kDAAkD;YAAAqC,QAAA,EAC9DxC,SAAS,IAAIsD,KAAK,CAACC,OAAO,CAACvD,SAAS,CAAC,GAAGA,SAAS,CAACwD,GAAG,CAAC,CAAC6C,QAAQ,EAAEhE,KAAK,KAAK;cAC1E;cACA,IAAI,CAACgE,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;gBAC7CvD,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEV,KAAK,EAAEgE,QAAQ,CAAC;gBAC3D,OAAO,IAAI;cACb;cAEA,oBACExG,OAAA;gBAEEwE,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAACC,KAAK,CAAE;gBACnClC,SAAS,EAAG;AAChC;AACA,wBAAwBkC,KAAK,KAAKhC,oBAAoB,GAC5B,wBAAwB,GACxBE,OAAO,CAAC8F,QAAQ,CAACxD,GAAG,CAAC,GACrB,6BAA6B,GAC7B,6CACH;AACvB,qBAAsB;gBAAAL,QAAA,EAEDH,KAAK,GAAG;cAAC,GAZLgE,QAAQ,CAACxD,GAAG,IAAIR,KAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAapB,CAAC;YAEb,CAAC,CAAC,GAAG;UAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGLvC,oBAAoB,KAAKU,cAAc,GAAG,CAAC,gBAC1ClB,OAAA;YACEwE,OAAO,EAAEA,CAAA,KAAMxD,oBAAoB,CAAC,IAAI,CAAE;YAC1CV,SAAS,EAAC,uHAAuH;YAAAqC,QAAA,gBAEjI3C,OAAA,CAACL,OAAO;cAACW,SAAS,EAAC;YAAS;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET/C,OAAA;YACEwE,OAAO,EAAEnC,QAAS;YAClB/B,SAAS,EAAC,qHAAqH;YAAAqC,QAAA,GAChI,MAEC,eAAA3C,OAAA,CAACN,cAAc;cAACY,SAAS,EAAC;YAAS;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA,CAACT,eAAe;MAAAoD,QAAA,EACb5B,iBAAiB,iBAChBf,OAAA,CAACV,MAAM,CAACyG,GAAG;QACTC,OAAO,EAAE;UAAEK,OAAO,EAAE;QAAE,CAAE;QACxBH,OAAO,EAAE;UAAEG,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrB/F,SAAS,EAAC,gFAAgF;QAAAqC,QAAA,eAE1F3C,OAAA,CAACV,MAAM,CAACyG,GAAG;UACTC,OAAO,EAAE;YAAE1B,KAAK,EAAE,GAAG;YAAE+B,OAAO,EAAE;UAAE,CAAE;UACpCH,OAAO,EAAE;YAAE5B,KAAK,EAAE,CAAC;YAAE+B,OAAO,EAAE;UAAE,CAAE;UAClCE,IAAI,EAAE;YAAEjC,KAAK,EAAE,GAAG;YAAE+B,OAAO,EAAE;UAAE,CAAE;UACjC/F,SAAS,EAAC,yCAAyC;UAAAqC,QAAA,gBAEnD3C,OAAA;YAAIM,SAAS,EAAC,sCAAsC;YAAAqC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtE/C,OAAA;YAAGM,SAAS,EAAC,oBAAoB;YAAAqC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ/C,OAAA;YAAKM,SAAS,EAAC,YAAY;YAAAqC,QAAA,gBACzB3C,OAAA;cACEwE,OAAO,EAAEA,CAAA,KAAMxD,oBAAoB,CAAC,KAAK,CAAE;cAC3CV,SAAS,EAAC,qGAAqG;cAAAqC,QAAA,EAChH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/C,OAAA;cACEwE,OAAO,EAAEnD,YAAa;cACtBf,SAAS,EAAC,0FAA0F;cAAAqC,QAAA,EACrG;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAACxC,EAAA,CA3eIN,aAAa;AAAA0G,EAAA,GAAb1G,aAAa;AA6enB,eAAeA,aAAa;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}