{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { TbSearch, TbFilter, TbClock, TbQuestionMark, TbTrophy, TbPlayerPlay, TbBrain, TbTarget, TbCheck, TbX, TbStar, TbHome, TbBolt } from 'react-icons/tb';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReportsByUser } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport QuizCard from '../../../components/modern/QuizCard';\nimport './responsive.css';\nimport './style.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = () => {\n  _s();\n  const [exams, setExams] = useState([]);\n  const [filteredExams, setFilteredExams] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [userResults, setUserResults] = useState({});\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getUserResults = async () => {\n    try {\n      if (!(user !== null && user !== void 0 && user._id)) return;\n      const response = await getAllReportsByUser({\n        userId: user._id\n      });\n      if (response.success) {\n        const resultsMap = {};\n        response.data.forEach(report => {\n          var _report$exam;\n          const examId = (_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam._id;\n          if (!examId || !report.result) return;\n\n          // Extract data from the result object\n          const result = report.result;\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\n            resultsMap[examId] = {\n              verdict: result.verdict,\n              percentage: result.percentage,\n              correctAnswers: result.correctAnswers,\n              wrongAnswers: result.wrongAnswers,\n              totalQuestions: result.totalQuestions,\n              obtainedMarks: result.obtainedMarks,\n              totalMarks: result.totalMarks,\n              score: result.score,\n              points: result.points,\n              xpEarned: result.xpEarned || 0,\n              timeTaken: report.timeTaken,\n              completedAt: report.createdAt\n            };\n          }\n        });\n        setUserResults(resultsMap);\n      }\n    } catch (error) {\n      console.error('Error fetching user results:', error);\n    }\n  };\n  useEffect(() => {\n    const getExams = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getAllExams();\n        dispatch(HideLoading());\n        if (response.success) {\n          // Filter exams by user's level\n          const userLevelExams = response.data.filter(exam => {\n            if (!exam.level || !user.level) return false;\n            return exam.level.toLowerCase() === user.level.toLowerCase();\n          });\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n          setExams(sortedExams);\n\n          // Set default class filter to user's class\n          if (user !== null && user !== void 0 && user.class) {\n            setSelectedClass(String(user.class));\n          }\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    getExams();\n    getUserResults();\n  }, [dispatch, user === null || user === void 0 ? void 0 : user.level, user === null || user === void 0 ? void 0 : user.class, user === null || user === void 0 ? void 0 : user._id]);\n\n  // Real-time updates for quiz completion\n  useEffect(() => {\n    // Listen for real-time updates from quiz completion\n    const handleRankingUpdate = () => {\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\n      getUserResults(); // Refresh user results to show updated XP\n    };\n\n    // Listen for window focus to refresh data when returning from quiz\n    const handleWindowFocus = () => {\n      console.log('🎯 Quiz listing - window focused, refreshing data...');\n      getUserResults();\n    };\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    window.addEventListener('focus', handleWindowFocus);\n    return () => {\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n      window.removeEventListener('focus', handleWindowFocus);\n    };\n  }, []);\n  useEffect(() => {\n    let filtered = exams;\n    if (searchTerm) {\n      filtered = filtered.filter(exam => {\n        var _exam$name, _exam$subject;\n        return ((_exam$name = exam.name) === null || _exam$name === void 0 ? void 0 : _exam$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.toLowerCase().includes(searchTerm.toLowerCase()));\n      });\n    }\n    if (selectedClass) {\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\n    }\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n    setFilteredExams(filtered);\n  }, [exams, searchTerm, selectedClass]);\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\n  const handleQuizStart = quiz => {\n    if (!quiz || !quiz._id) {\n      message.error('Invalid quiz selected. Please try again.');\n      return;\n    }\n    navigate(`/quiz/${quiz._id}/play`);\n  };\n  const handleQuizView = quiz => {\n    if (!quiz || !quiz._id) {\n      message.error('Invalid quiz selected. Please try again.');\n      return;\n    }\n    // Check if user has attempted this quiz\n    const userResult = userResults[quiz._id];\n    if (!userResult) {\n      message.info('You need to attempt this quiz first to view results.');\n      return;\n    }\n    navigate(`/quiz/${quiz._id}/result`);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading quizzes...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-responsive\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8 sm:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbBrain, {\n            className: \"w-8 h-8 sm:w-10 sm:h-10 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\",\n          children: \"Challenge Your Brain, Beat the Rest\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\",\n          children: [\"Test your knowledge with our comprehensive quizzes designed for Class \", (user === null || user === void 0 ? void 0 : user.class) || 'All Classes']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-green-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [filteredExams.length, \" Available Quizzes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-blue-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Level: \", (user === null || user === void 0 ? void 0 : user.level) || 'All Levels']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"max-w-4xl mx-auto mb-8 sm:mb-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-4 sm:p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-3 sm:gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(TbSearch, {\n                  className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search quizzes by name or subject...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sm:w-48 md:w-64\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(TbFilter, {\n                    className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: selectedClass,\n                  onChange: e => setSelectedClass(e.target.value),\n                  className: \"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Classes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 21\n                  }, this), availableClasses.map(className => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: className,\n                    children: [\"Class \", className]\n                  }, className, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"quiz-listing-content\",\n        children: filteredExams.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12 sm:py-16\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n              className: \"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-2\",\n              children: \"No Quizzes Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm sm:text-base\",\n              children: searchTerm || selectedClass ? \"Try adjusting your search or filter criteria.\" : \"No quizzes are available for your level at the moment.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"quiz-grid-container\",\n          children: filteredExams.map((quiz, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: Math.min(index * 0.1, 0.8)\n            },\n            className: \"h-full\",\n            children: /*#__PURE__*/_jsxDEV(QuizCard, {\n              quiz: quiz,\n              userResult: userResults[quiz._id],\n              showResults: true,\n              onStart: handleQuizStart,\n              onView: () => handleQuizView(quiz)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this)\n          }, quiz._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n_s(Quiz, \"smeEF9GR69BdE1ZVtjkix2mojGc=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = Quiz;\nexport default Quiz;\nvar _c;\n$RefreshReg$(_c, \"Quiz\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useDispatch", "useSelector", "motion", "message", "TbSearch", "Tb<PERSON><PERSON>er", "TbClock", "TbQuestionMark", "TbTrophy", "TbPlayerPlay", "TbBrain", "TbTarget", "TbCheck", "TbX", "TbStar", "TbHome", "TbBolt", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "QuizCard", "jsxDEV", "_jsxDEV", "Quiz", "_s", "exams", "setExams", "filteredExams", "setFilteredExams", "searchTerm", "setSearchTerm", "selectedClass", "setSelectedClass", "userResults", "setUserResults", "loading", "setLoading", "navigate", "dispatch", "user", "state", "getUserResults", "_id", "response", "userId", "success", "resultsMap", "data", "for<PERSON>ach", "report", "_report$exam", "examId", "exam", "result", "Date", "createdAt", "verdict", "percentage", "correctAnswers", "wrongAnswers", "totalQuestions", "obtainedMarks", "totalMarks", "score", "points", "xpEarned", "timeTaken", "completedAt", "error", "console", "getExams", "userLevelExams", "filter", "level", "toLowerCase", "sortedExams", "sort", "a", "b", "class", "String", "handleRankingUpdate", "log", "handleWindowFocus", "window", "addEventListener", "removeEventListener", "filtered", "_exam$name", "_exam$subject", "name", "includes", "subject", "availableClasses", "Set", "map", "e", "Boolean", "handleQuizStart", "quiz", "handleQuizView", "userResult", "info", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "length", "transition", "delay", "type", "placeholder", "value", "onChange", "target", "index", "Math", "min", "showResults", "onStart", "onView", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { motion } from 'framer-motion';\r\nimport { message } from 'antd';\r\nimport {\r\n  Tb<PERSON><PERSON>ch,\r\n  Tb<PERSON>ilter,\r\n  TbClock,\r\n  TbQuestionMark,\r\n  TbTrophy,\r\n  TbPlayerPlay,\r\n  TbBrain,\r\n  TbTarget,\r\n  TbCheck,\r\n  TbX,\r\n  TbStar,\r\n  TbHome,\r\n  TbBolt\r\n} from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { getAllReportsByUser } from '../../../apicalls/reports';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport QuizCard from '../../../components/modern/QuizCard';\r\nimport './responsive.css';\r\nimport './style.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedClass, setSelectedClass] = useState('');\r\n  const [userResults, setUserResults] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const getUserResults = async () => {\r\n    try {\r\n      if (!user?._id) return;\r\n\r\n      const response = await getAllReportsByUser({ userId: user._id });\r\n\r\n      if (response.success) {\r\n        const resultsMap = {};\r\n        response.data.forEach(report => {\r\n          const examId = report.exam?._id;\r\n          if (!examId || !report.result) return;\r\n\r\n          // Extract data from the result object\r\n          const result = report.result;\r\n\r\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\r\n            resultsMap[examId] = {\r\n              verdict: result.verdict,\r\n              percentage: result.percentage,\r\n              correctAnswers: result.correctAnswers,\r\n              wrongAnswers: result.wrongAnswers,\r\n              totalQuestions: result.totalQuestions,\r\n              obtainedMarks: result.obtainedMarks,\r\n              totalMarks: result.totalMarks,\r\n              score: result.score,\r\n              points: result.points,\r\n              xpEarned: result.xpEarned || 0,\r\n              timeTaken: report.timeTaken,\r\n              completedAt: report.createdAt,\r\n            };\r\n          }\r\n        });\r\n        setUserResults(resultsMap);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching user results:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const getExams = async () => {\r\n      try {\r\n        dispatch(ShowLoading());\r\n        const response = await getAllExams();\r\n        dispatch(HideLoading());\r\n\r\n        if (response.success) {\r\n          // Filter exams by user's level\r\n          const userLevelExams = response.data.filter(exam => {\r\n            if (!exam.level || !user.level) return false;\r\n            return exam.level.toLowerCase() === user.level.toLowerCase();\r\n          });\r\n\r\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n          setExams(sortedExams);\r\n\r\n          // Set default class filter to user's class\r\n          if (user?.class) {\r\n            setSelectedClass(String(user.class));\r\n          }\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    getExams();\r\n    getUserResults();\r\n  }, [dispatch, user?.level, user?.class, user?._id]);\r\n\r\n  // Real-time updates for quiz completion\r\n  useEffect(() => {\r\n    // Listen for real-time updates from quiz completion\r\n    const handleRankingUpdate = () => {\r\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\r\n      getUserResults(); // Refresh user results to show updated XP\r\n    };\r\n\r\n    // Listen for window focus to refresh data when returning from quiz\r\n    const handleWindowFocus = () => {\r\n      console.log('🎯 Quiz listing - window focused, refreshing data...');\r\n      getUserResults();\r\n    };\r\n\r\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\r\n    window.addEventListener('focus', handleWindowFocus);\r\n\r\n    return () => {\r\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\r\n      window.removeEventListener('focus', handleWindowFocus);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    let filtered = exams;\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(exam =>\r\n        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n    if (selectedClass) {\r\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\r\n    }\r\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n    setFilteredExams(filtered);\r\n  }, [exams, searchTerm, selectedClass]);\r\n\r\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n    navigate(`/quiz/${quiz._id}/play`);\r\n  };\r\n\r\n  const handleQuizView = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n    // Check if user has attempted this quiz\r\n    const userResult = userResults[quiz._id];\r\n    if (!userResult) {\r\n      message.info('You need to attempt this quiz first to view results.');\r\n      return;\r\n    }\r\n    navigate(`/quiz/${quiz._id}/result`);\r\n  };\r\n\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading quizzes...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-responsive\">\r\n\r\n      <div className=\"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Hero Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"text-center mb-8 sm:mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\">\r\n            <TbBrain className=\"w-8 h-8 sm:w-10 sm:h-10 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Challenge Your Brain, Beat the Rest\r\n          </h1>\r\n          <p className=\"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\">\r\n            Test your knowledge with our comprehensive quizzes designed for Class {user?.class || 'All Classes'}\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n              <span>{filteredExams.length} Available Quizzes</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n              <span>Level: {user?.level || 'All Levels'}</span>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Search and Filter */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"max-w-4xl mx-auto mb-8 sm:mb-12\"\r\n        >\r\n          <div className=\"bg-white rounded-2xl shadow-lg p-4 sm:p-6\">\r\n            <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                  <TbSearch className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search quizzes by name or subject...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"\r\n                />\r\n              </div>\r\n              <div className=\"sm:w-48 md:w-64\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                    <TbFilter className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                  </div>\r\n                  <select\r\n                    value={selectedClass}\r\n                    onChange={(e) => setSelectedClass(e.target.value)}\r\n                    className=\"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\"\r\n                  >\r\n                    <option value=\"\">All Classes</option>\r\n                    {availableClasses.map((className) => (\r\n                      <option key={className} value={className}>Class {className}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Quiz Grid */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"quiz-listing-content\"\r\n        >\r\n          {filteredExams.length === 0 ? (\r\n            <div className=\"text-center py-12 sm:py-16\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\">\r\n                <TbTarget className=\"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600 text-sm sm:text-base\">\r\n                  {searchTerm || selectedClass\r\n                    ? \"Try adjusting your search or filter criteria.\"\r\n                    : \"No quizzes are available for your level at the moment.\"\r\n                  }\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"quiz-grid-container\">\r\n              {filteredExams.map((quiz, index) => (\r\n                <motion.div\r\n                  key={quiz._id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: Math.min(index * 0.1, 0.8) }}\r\n                  className=\"h-full\"\r\n                >\r\n                  <QuizCard\r\n                    quiz={quiz}\r\n                    userResult={userResults[quiz._id]}\r\n                    showResults={true}\r\n                    onStart={handleQuizStart}\r\n                    onView={() => handleQuizView(quiz)}\r\n                  />\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,QAAQ,MAAM,qCAAqC;AAC1D,OAAO,kBAAkB;AACzB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMyC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAMwC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwC;EAAK,CAAC,GAAGvC,WAAW,CAAEwC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,IAAI,EAACF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,GAAG,GAAE;MAEhB,MAAMC,QAAQ,GAAG,MAAM1B,mBAAmB,CAAC;QAAE2B,MAAM,EAAEL,IAAI,CAACG;MAAI,CAAC,CAAC;MAEhE,IAAIC,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,UAAU,GAAG,CAAC,CAAC;QACrBH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAACC,MAAM,IAAI;UAAA,IAAAC,YAAA;UAC9B,MAAMC,MAAM,IAAAD,YAAA,GAAGD,MAAM,CAACG,IAAI,cAAAF,YAAA,uBAAXA,YAAA,CAAaR,GAAG;UAC/B,IAAI,CAACS,MAAM,IAAI,CAACF,MAAM,CAACI,MAAM,EAAE;;UAE/B;UACA,MAAMA,MAAM,GAAGJ,MAAM,CAACI,MAAM;UAE5B,IAAI,CAACP,UAAU,CAACK,MAAM,CAAC,IAAI,IAAIG,IAAI,CAACL,MAAM,CAACM,SAAS,CAAC,GAAG,IAAID,IAAI,CAACR,UAAU,CAACK,MAAM,CAAC,CAACI,SAAS,CAAC,EAAE;YAC9FT,UAAU,CAACK,MAAM,CAAC,GAAG;cACnBK,OAAO,EAAEH,MAAM,CAACG,OAAO;cACvBC,UAAU,EAAEJ,MAAM,CAACI,UAAU;cAC7BC,cAAc,EAAEL,MAAM,CAACK,cAAc;cACrCC,YAAY,EAAEN,MAAM,CAACM,YAAY;cACjCC,cAAc,EAAEP,MAAM,CAACO,cAAc;cACrCC,aAAa,EAAER,MAAM,CAACQ,aAAa;cACnCC,UAAU,EAAET,MAAM,CAACS,UAAU;cAC7BC,KAAK,EAAEV,MAAM,CAACU,KAAK;cACnBC,MAAM,EAAEX,MAAM,CAACW,MAAM;cACrBC,QAAQ,EAAEZ,MAAM,CAACY,QAAQ,IAAI,CAAC;cAC9BC,SAAS,EAAEjB,MAAM,CAACiB,SAAS;cAC3BC,WAAW,EAAElB,MAAM,CAACM;YACtB,CAAC;UACH;QACF,CAAC,CAAC;QACFrB,cAAc,CAACY,UAAU,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAEDvE,SAAS,CAAC,MAAM;IACd,MAAMyE,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFhC,QAAQ,CAACnB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAMwB,QAAQ,GAAG,MAAM3B,WAAW,CAAC,CAAC;QACpCsB,QAAQ,CAACpB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAIyB,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAM0B,cAAc,GAAG5B,QAAQ,CAACI,IAAI,CAACyB,MAAM,CAACpB,IAAI,IAAI;YAClD,IAAI,CAACA,IAAI,CAACqB,KAAK,IAAI,CAAClC,IAAI,CAACkC,KAAK,EAAE,OAAO,KAAK;YAC5C,OAAOrB,IAAI,CAACqB,KAAK,CAACC,WAAW,CAAC,CAAC,KAAKnC,IAAI,CAACkC,KAAK,CAACC,WAAW,CAAC,CAAC;UAC9D,CAAC,CAAC;UAEF,MAAMC,WAAW,GAAGJ,cAAc,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIxB,IAAI,CAACwB,CAAC,CAACvB,SAAS,CAAC,GAAG,IAAID,IAAI,CAACuB,CAAC,CAACtB,SAAS,CAAC,CAAC;UAChG7B,QAAQ,CAACiD,WAAW,CAAC;;UAErB;UACA,IAAIpC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwC,KAAK,EAAE;YACf/C,gBAAgB,CAACgD,MAAM,CAACzC,IAAI,CAACwC,KAAK,CAAC,CAAC;UACtC;QACF,CAAC,MAAM;UACL7E,OAAO,CAACkE,KAAK,CAACzB,QAAQ,CAACzC,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAOkE,KAAK,EAAE;QACd9B,QAAQ,CAACpB,WAAW,CAAC,CAAC,CAAC;QACvBhB,OAAO,CAACkE,KAAK,CAACA,KAAK,CAAClE,OAAO,CAAC;MAC9B,CAAC,SAAS;QACRkC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDkC,QAAQ,CAAC,CAAC;IACV7B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACH,QAAQ,EAAEC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,KAAK,EAAElC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,KAAK,EAAExC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,GAAG,CAAC,CAAC;;EAEnD;EACA7C,SAAS,CAAC,MAAM;IACd;IACA,MAAMoF,mBAAmB,GAAGA,CAAA,KAAM;MAChCZ,OAAO,CAACa,GAAG,CAAC,4DAA4D,CAAC;MACzEzC,cAAc,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAM0C,iBAAiB,GAAGA,CAAA,KAAM;MAC9Bd,OAAO,CAACa,GAAG,CAAC,sDAAsD,CAAC;MACnEzC,cAAc,CAAC,CAAC;IAClB,CAAC;IAED2C,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEJ,mBAAmB,CAAC;IAC7DG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;IAEnD,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,eAAe,EAAEL,mBAAmB,CAAC;MAChEG,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEH,iBAAiB,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENtF,SAAS,CAAC,MAAM;IACd,IAAI0F,QAAQ,GAAG9D,KAAK;IACpB,IAAII,UAAU,EAAE;MACd0D,QAAQ,GAAGA,QAAQ,CAACf,MAAM,CAACpB,IAAI;QAAA,IAAAoC,UAAA,EAAAC,aAAA;QAAA,OAC7B,EAAAD,UAAA,GAAApC,IAAI,CAACsC,IAAI,cAAAF,UAAA,uBAATA,UAAA,CAAWd,WAAW,CAAC,CAAC,CAACiB,QAAQ,CAAC9D,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC,OAAAe,aAAA,GAC3DrC,IAAI,CAACwC,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAcf,WAAW,CAAC,CAAC,CAACiB,QAAQ,CAAC9D,UAAU,CAAC6C,WAAW,CAAC,CAAC,CAAC;MAAA,CAChE,CAAC;IACH;IACA,IAAI3C,aAAa,EAAE;MACjBwD,QAAQ,GAAGA,QAAQ,CAACf,MAAM,CAACpB,IAAI,IAAI4B,MAAM,CAAC5B,IAAI,CAAC2B,KAAK,CAAC,KAAKC,MAAM,CAACjD,aAAa,CAAC,CAAC;IAClF;IACAwD,QAAQ,CAACX,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIxB,IAAI,CAACwB,CAAC,CAACvB,SAAS,CAAC,GAAG,IAAID,IAAI,CAACuB,CAAC,CAACtB,SAAS,CAAC,CAAC;IACtE3B,gBAAgB,CAAC2D,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAAC9D,KAAK,EAAEI,UAAU,EAAEE,aAAa,CAAC,CAAC;EAEtC,MAAM8D,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACrE,KAAK,CAACsE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,KAAK,CAAC,CAACP,MAAM,CAACyB,OAAO,CAAC,CAAC,CAAC,CAACrB,IAAI,CAAC,CAAC;EAErF,MAAMsB,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACzD,GAAG,EAAE;MACtBxC,OAAO,CAACkE,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;IACA/B,QAAQ,CAAE,SAAQ8D,IAAI,CAACzD,GAAI,OAAM,CAAC;EACpC,CAAC;EAED,MAAM0D,cAAc,GAAID,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACzD,GAAG,EAAE;MACtBxC,OAAO,CAACkE,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;IACA;IACA,MAAMiC,UAAU,GAAGpE,WAAW,CAACkE,IAAI,CAACzD,GAAG,CAAC;IACxC,IAAI,CAAC2D,UAAU,EAAE;MACfnG,OAAO,CAACoG,IAAI,CAAC,sDAAsD,CAAC;MACpE;IACF;IACAjE,QAAQ,CAAE,SAAQ8D,IAAI,CAACzD,GAAI,SAAQ,CAAC;EACtC,CAAC;EAID,IAAIP,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKiF,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGlF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlF,OAAA;UAAKiF,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGtF,OAAA;UAAGiF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtF,OAAA;IAAKiF,SAAS,EAAC,2EAA2E;IAAAC,QAAA,eAExFlF,OAAA;MAAKiF,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAE1ElF,OAAA,CAACrB,MAAM,CAAC4G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BT,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAErClF,OAAA;UAAKiF,SAAS,EAAC,qJAAqJ;UAAAC,QAAA,eAClKlF,OAAA,CAACb,OAAO;YAAC8F,SAAS,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNtF,OAAA;UAAIiF,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EAAC;QAEvG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtF,OAAA;UAAGiF,SAAS,EAAC,mFAAmF;UAAAC,QAAA,GAAC,wEACzB,EAAC,CAAAjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,KAAK,KAAI,aAAa;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,eACJtF,OAAA;UAAKiF,SAAS,EAAC,iGAAiG;UAAAC,QAAA,gBAC9GlF,OAAA;YAAKiF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClF,OAAA;cAAKiF,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDtF,OAAA;cAAAkF,QAAA,GAAO7E,aAAa,CAACuF,MAAM,EAAC,oBAAkB;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNtF,OAAA;YAAKiF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtClF,OAAA;cAAKiF,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDtF,OAAA;cAAAkF,QAAA,GAAM,SAAO,EAAC,CAAAjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,KAAK,KAAI,YAAY;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbtF,OAAA,CAACrB,MAAM,CAAC4G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3Bb,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAE3ClF,OAAA;UAAKiF,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eACxDlF,OAAA;YAAKiF,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDlF,OAAA;cAAKiF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BlF,OAAA;gBAAKiF,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,eAC3FlF,OAAA,CAACnB,QAAQ;kBAACoG,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNtF,OAAA;gBACE+F,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,sCAAsC;gBAClDC,KAAK,EAAE1F,UAAW;gBAClB2F,QAAQ,EAAGxB,CAAC,IAAKlE,aAAa,CAACkE,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAE;gBAC/ChB,SAAS,EAAC;cAAmO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9O,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtF,OAAA;cAAKiF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BlF,OAAA;gBAAKiF,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBlF,OAAA;kBAAKiF,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,eAC3FlF,OAAA,CAAClB,QAAQ;oBAACmG,SAAS,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACNtF,OAAA;kBACEiG,KAAK,EAAExF,aAAc;kBACrByF,QAAQ,EAAGxB,CAAC,IAAKhE,gBAAgB,CAACgE,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAE;kBAClDhB,SAAS,EAAC,2OAA2O;kBAAAC,QAAA,gBAErPlF,OAAA;oBAAQiG,KAAK,EAAC,EAAE;oBAAAf,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpCf,gBAAgB,CAACE,GAAG,CAAEQ,SAAS,iBAC9BjF,OAAA;oBAAwBiG,KAAK,EAAEhB,SAAU;oBAAAC,QAAA,GAAC,QAAM,EAACD,SAAS;kBAAA,GAA7CA,SAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6C,CACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbtF,OAAA,CAACrB,MAAM,CAAC4G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BG,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3Bb,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAE/B7E,aAAa,CAACuF,MAAM,KAAK,CAAC,gBACzB5F,OAAA;UAAKiF,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzClF,OAAA;YAAKiF,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1ElF,OAAA,CAACZ,QAAQ;cAAC6F,SAAS,EAAC;YAAsD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7EtF,OAAA;cAAIiF,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzFtF,OAAA;cAAGiF,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAC9C3E,UAAU,IAAIE,aAAa,GACxB,+CAA+C,GAC/C;YAAwD;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENtF,OAAA;UAAKiF,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EACjC7E,aAAa,CAACoE,GAAG,CAAC,CAACI,IAAI,EAAEuB,KAAK,kBAC7BpG,OAAA,CAACrB,MAAM,CAAC4G,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,KAAK,EAAEO,IAAI,CAACC,GAAG,CAACF,KAAK,GAAG,GAAG,EAAE,GAAG;YAAE,CAAE;YAClDnB,SAAS,EAAC,QAAQ;YAAAC,QAAA,eAElBlF,OAAA,CAACF,QAAQ;cACP+E,IAAI,EAAEA,IAAK;cACXE,UAAU,EAAEpE,WAAW,CAACkE,IAAI,CAACzD,GAAG,CAAE;cAClCmF,WAAW,EAAE,IAAK;cAClBC,OAAO,EAAE5B,eAAgB;cACzB6B,MAAM,EAAEA,CAAA,KAAM3B,cAAc,CAACD,IAAI;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC,GAZGT,IAAI,CAACzD,GAAG;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaH,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpF,EAAA,CAvRID,IAAI;EAAA,QAOSzB,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAAgI,EAAA,GATxBzG,IAAI;AAyRV,eAAeA,IAAI;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}