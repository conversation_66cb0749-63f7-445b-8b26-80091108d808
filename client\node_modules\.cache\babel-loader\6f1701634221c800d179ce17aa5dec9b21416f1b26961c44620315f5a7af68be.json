{"ast": null, "code": "import axiosInstance from \"./index\";\n\n// Get all quizzes (using exams endpoint)\nexport const getAllQuizzes = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/exams/get-all-exams\");\n    return response.data;\n  } catch (error) {\n    var _error$response;\n    return ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};\n\n// Get quiz by ID (using exam endpoint)\nexport const getQuizById = async quizId => {\n  try {\n    const response = await axiosInstance.post(\"/api/exams/get-exam-by-id\", {\n      examId: quizId\n    });\n    return response.data;\n  } catch (error) {\n    var _error$response2;\n    return ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};\n\n// Submit quiz result (using reports endpoint)\nexport const submitQuizResult = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\n    return response.data;\n  } catch (error) {\n    var _error$response3;\n    return ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};\n\n// Get user results (using reports endpoint)\nexport const getUserResults = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/get-all-reports\");\n    return response.data;\n  } catch (error) {\n    var _error$response4;\n    return ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};\n\n// Get quiz analytics\nexport const getQuizAnalytics = async userId => {\n  try {\n    const response = await axiosInstance.get(`/api/enhanced-quiz/analytics/${userId}`);\n    return response.data;\n  } catch (error) {\n    var _error$response5;\n    return ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};\n\n// Get leaderboard\nexport const getLeaderboard = async (options = {}) => {\n  try {\n    const queryParams = new URLSearchParams(options).toString();\n    const response = await axiosInstance.get(`/api/enhanced-quiz/xp-leaderboard?${queryParams}`);\n    return response.data;\n  } catch (error) {\n    var _error$response6;\n    return ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};\n\n// Enhanced quiz scoring\nexport const calculateEnhancedScore = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/enhanced-quiz/calculate-enhanced-score\", payload);\n    return response.data;\n  } catch (error) {\n    var _error$response7;\n    return ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};", "map": {"version": 3, "names": ["axiosInstance", "getAllQuizzes", "response", "post", "data", "error", "_error$response", "success", "message", "getQuizById", "quizId", "examId", "_error$response2", "submitQuizResult", "payload", "_error$response3", "getUserResults", "_error$response4", "getQuizAnalytics", "userId", "get", "_error$response5", "getLeaderboard", "options", "queryParams", "URLSearchParams", "toString", "_error$response6", "calculateEnhancedScore", "_error$response7"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/quiz.js"], "sourcesContent": ["import axiosInstance from \"./index\";\n\n// Get all quizzes (using exams endpoint)\nexport const getAllQuizzes = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/exams/get-all-exams\");\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Get quiz by ID (using exam endpoint)\nexport const getQuizById = async (quizId) => {\n  try {\n    const response = await axiosInstance.post(\"/api/exams/get-exam-by-id\", {\n      examId: quizId\n    });\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Submit quiz result (using reports endpoint)\nexport const submitQuizResult = async (payload) => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Get user results (using reports endpoint)\nexport const getUserResults = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/get-all-reports\");\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Get quiz analytics\nexport const getQuizAnalytics = async (userId) => {\n  try {\n    const response = await axiosInstance.get(`/api/enhanced-quiz/analytics/${userId}`);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Get leaderboard\nexport const getLeaderboard = async (options = {}) => {\n  try {\n    const queryParams = new URLSearchParams(options).toString();\n    const response = await axiosInstance.get(`/api/enhanced-quiz/xp-leaderboard?${queryParams}`);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Enhanced quiz scoring\nexport const calculateEnhancedScore = async (payload) => {\n  try {\n    const response = await axiosInstance.post(\"/api/enhanced-quiz/calculate-enhanced-score\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,SAAS;;AAEnC;AACA,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMF,aAAa,CAACG,IAAI,CAAC,0BAA0B,CAAC;IACrE,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAC,eAAA;IACd,OAAO,EAAAA,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBF,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG,MAAOC,MAAM,IAAK;EAC3C,IAAI;IACF,MAAMR,QAAQ,GAAG,MAAMF,aAAa,CAACG,IAAI,CAAC,2BAA2B,EAAE;MACrEQ,MAAM,EAAED;IACV,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAO,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAP,KAAK,CAACH,QAAQ,cAAAU,gBAAA,uBAAdA,gBAAA,CAAgBR,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,gBAAgB,GAAG,MAAOC,OAAO,IAAK;EACjD,IAAI;IACF,MAAMZ,QAAQ,GAAG,MAAMF,aAAa,CAACG,IAAI,CAAC,yBAAyB,EAAEW,OAAO,CAAC;IAC7E,OAAOZ,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAU,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAV,KAAK,CAACH,QAAQ,cAAAa,gBAAA,uBAAdA,gBAAA,CAAgBX,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMd,QAAQ,GAAG,MAAMF,aAAa,CAACG,IAAI,CAAC,8BAA8B,CAAC;IACzE,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAY,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAZ,KAAK,CAACH,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBb,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,gBAAgB,GAAG,MAAOC,MAAM,IAAK;EAChD,IAAI;IACF,MAAMjB,QAAQ,GAAG,MAAMF,aAAa,CAACoB,GAAG,CAAE,gCAA+BD,MAAO,EAAC,CAAC;IAClF,OAAOjB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAgB,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAhB,KAAK,CAACH,QAAQ,cAAAmB,gBAAA,uBAAdA,gBAAA,CAAgBjB,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,cAAc,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,CAAC,KAAK;EACpD,IAAI;IACF,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,OAAO,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC3D,MAAMxB,QAAQ,GAAG,MAAMF,aAAa,CAACoB,GAAG,CAAE,qCAAoCI,WAAY,EAAC,CAAC;IAC5F,OAAOtB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAsB,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAtB,KAAK,CAACH,QAAQ,cAAAyB,gBAAA,uBAAdA,gBAAA,CAAgBvB,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC;;AAED;AACA,OAAO,MAAMoB,sBAAsB,GAAG,MAAOd,OAAO,IAAK;EACvD,IAAI;IACF,MAAMZ,QAAQ,GAAG,MAAMF,aAAa,CAACG,IAAI,CAAC,6CAA6C,EAAEW,OAAO,CAAC;IACjG,OAAOZ,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAwB,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAxB,KAAK,CAACH,QAAQ,cAAA2B,gBAAA,uBAAdA,gBAAA,CAAgBzB,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}